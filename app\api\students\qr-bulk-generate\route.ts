import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  qrGenerateSchema,
  type QRGenerateData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse 
} from '@/lib/types/api'
import { qrGenerator, qrManager, qrPrinter } from '@/lib/utils/qr-code'
import { GradeLevel, StudentStatus } from '@/lib/generated/prisma'

// POST /api/students/qr-bulk-generate - Bulk QR code generation
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.update')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    
    // Validate QR generation data
    const validation = qrGenerateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid QR generation data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const validatedData = validation.data
    const options = validatedData.options || {}

    // Determine which students to generate QR codes for
    let studentIds: string[] = []
    
    if (validatedData.studentIds) {
      studentIds = validatedData.studentIds
    } else if (validatedData.studentId) {
      studentIds = [validatedData.studentId]
    } else {
      // If no specific students provided, check for filters in the request
      const { filters } = body
      
      if (filters) {
        // Build where clause for filtering
        const where: any = {}
        
        if (filters.gradeLevel?.length) {
          where.gradeLevel = { in: filters.gradeLevel as GradeLevel[] }
        }
        
        if (filters.section?.length) {
          where.section = { in: filters.section }
        }
        
        if (filters.status?.length) {
          where.status = { in: filters.status as StudentStatus[] }
        } else {
          // Default to active students only
          where.status = 'ACTIVE'
        }
        
        if (filters.course?.length) {
          where.course = { in: filters.course }
        }
        
        if (filters.year?.length) {
          where.year = { in: filters.year }
        }

        // Get student IDs based on filters
        const filteredStudents = await prisma.student.findMany({
          where,
          select: { id: true },
          take: 100 // Limit to prevent overwhelming the system
        })
        
        studentIds = filteredStudents.map(s => s.id)
      }
    }

    if (studentIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No students specified for QR generation'
      } as ApiResponse, { status: 400 })
    }

    if (studentIds.length > 100) {
      return NextResponse.json({
        success: false,
        error: 'Cannot generate QR codes for more than 100 students at once'
      } as ApiResponse, { status: 400 })
    }

    // Fetch students
    const students = await prisma.student.findMany({
      where: { id: { in: studentIds } }
    })

    const foundStudentIds = new Set(students.map(s => s.id))
    const notFoundIds = studentIds.filter(id => !foundStudentIds.has(id))

    if (notFoundIds.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Students not found: ${notFoundIds.join(', ')}`
      } as ApiResponse, { status: 404 })
    }

    // Calculate expiration date
    const expiresIn = options.expiresIn || 365 // days
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + expiresIn)

    // Generate QR codes for all students
    const results = await prisma.$transaction(async (tx) => {
      const qrResults = []
      
      for (const student of students) {
        try {
          // Convert to legacy format for QR generation
          const legacyStudent = convertPrismaStudentToLegacy(student as any)

          // Generate QR code data
          const qrString = qrGenerator.generateQRString(legacyStudent)
          const qrId = qrGenerator.generateQRCodeId(legacyStudent)

          // Create QR code data with encryption if requested
          let qrCodeData: string
          if (options.format === 'encrypted') {
            qrCodeData = Buffer.from(qrString).toString('base64')
          } else {
            qrCodeData = qrString
          }

          // Update student record with QR code data
          await tx.student.update({
            where: { id: student.id },
            data: {
              qrCodeData: qrCodeData,
              updatedAt: new Date()
            }
          })

          // Generate actual QR code
          const qrCodeId = await qrManager.generateForStudent(legacyStudent)

          qrResults.push({
            studentId: student.id,
            studentNumber: student.studentId,
            name: `${student.firstName} ${student.lastName}`,
            qrCodeId,
            qrData: options.format === 'json' ? JSON.parse(qrString) : qrCodeData,
            success: true
          })

        } catch (error) {
          qrResults.push({
            studentId: student.id,
            studentNumber: student.studentId,
            name: `${student.firstName} ${student.lastName}`,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return qrResults
    })

    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)

    // Generate print layout if requested
    let printLayout = null
    if (body.generatePrintLayout && successful.length > 0) {
      const successfulStudents = students.filter(s => 
        successful.some(r => r.studentId === s.id)
      )
      const legacyStudents = successfulStudents.map(s => convertPrismaStudentToLegacy(s as any))
      
      printLayout = qrPrinter.generatePrintLayout(legacyStudents, {
        size: options.size || 'medium',
        includeStudentInfo: true,
        includeSchoolLogo: true,
        codesPerPage: 12
      })
    }

    // Log audit trail for bulk QR generation
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'QR_BULK_GENERATE',
        entityType: 'Student',
        entityId: 'bulk',
        changes: JSON.stringify({
          totalStudents: students.length,
          successful: successful.length,
          failed: failed.length,
          options,
          filters: body.filters
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: {
          total: students.length,
          successful: successful.length,
          failed: failed.length
        },
        printLayout,
        options: {
          format: options.format || 'encrypted',
          size: options.size || 'medium',
          expiresAt: expiresAt.toISOString(),
          generatedAt: new Date().toISOString()
        }
      },
      message: `QR codes generated: ${successful.length} successful, ${failed.length} failed`
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Bulk QR generation error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// GET /api/students/qr-bulk-generate - Get bulk QR generation status/history
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Get recent bulk QR generation audit logs
    const recentGenerations = await prisma.auditLog.findMany({
      where: {
        action: 'QR_BULK_GENERATE',
        entityType: 'Student'
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            username: true
          }
        }
      }
    })

    // Get current QR code statistics
    const qrStats = await prisma.student.aggregate({
      _count: {
        qrCodeData: true
      },
      where: {
        qrCodeData: { not: null },
        status: 'ACTIVE'
      }
    })

    const totalActiveStudents = await prisma.student.count({
      where: { status: 'ACTIVE' }
    })

    return NextResponse.json({
      success: true,
      data: {
        recentGenerations: recentGenerations.map(log => ({
          id: log.id,
          generatedAt: log.createdAt,
          generatedBy: log.user ? `${log.user.firstName} ${log.user.lastName}` : 'Unknown',
          summary: JSON.parse(log.changes || '{}'),
          ipAddress: log.ipAddress
        })),
        statistics: {
          totalActiveStudents,
          studentsWithQR: qrStats._count.qrCodeData,
          studentsWithoutQR: totalActiveStudents - qrStats._count.qrCodeData,
          qrCoverage: totalActiveStudents > 0 ? (qrStats._count.qrCodeData / totalActiveStudents) * 100 : 0
        }
      },
      message: 'Bulk QR generation history retrieved successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Bulk QR generation history error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
