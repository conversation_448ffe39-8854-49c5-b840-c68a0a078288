// Jest Setup File for Student Management API Tests
// Global test setup and configuration

import { jest } from '@jest/globals'

// ============================================================================
// GLOBAL TEST CONFIGURATION
// ============================================================================

// Increase timeout for integration tests
jest.setTimeout(30000)

// ============================================================================
// ENVIRONMENT SETUP
// ============================================================================

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = 'file:./test.db'
process.env.NEXTAUTH_SECRET = 'test-secret'
process.env.NEXTAUTH_URL = 'http://localhost:3000'

// ============================================================================
// GLOBAL MOCKS
// ============================================================================

// Mock Next.js auth
jest.mock('next-auth', () => ({
  default: jest.fn(),
  getServerSession: jest.fn()
}))

// Mock Next.js auth config
jest.mock('@/lib/auth/config', () => ({
  auth: jest.fn(),
  signIn: jest.fn(),
  signOut: jest.fn()
}))

// Mock permissions
jest.mock('@/lib/auth/permissions', () => ({
  hasPermission: jest.fn().mockReturnValue(true),
  checkPermissions: jest.fn().mockReturnValue(true),
  getUserPermissions: jest.fn().mockReturnValue(['students:read', 'students:write', 'students:delete'])
}))

// Mock Prisma client for unit tests
jest.mock('@/lib/prisma', () => ({
  prisma: {
    student: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      createMany: jest.fn(),
      updateMany: jest.fn(),
      deleteMany: jest.fn()
    },
    attendanceRecord: {
      findMany: jest.fn(),
      create: jest.fn(),
      count: jest.fn()
    },
    auditLog: {
      create: jest.fn()
    },
    $transaction: jest.fn(),
    $connect: jest.fn(),
    $disconnect: jest.fn()
  }
}))

// Mock QR code utilities
jest.mock('qrcode', () => ({
  toDataURL: jest.fn().mockResolvedValue('data:image/png;base64,mock-qr-code'),
  toString: jest.fn().mockResolvedValue('mock-qr-string')
}))

// Mock crypto for QR code encryption
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'mock-uuid-1234-5678-9012'),
    getRandomValues: jest.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    }),
    subtle: {
      encrypt: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
      decrypt: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
      generateKey: jest.fn().mockResolvedValue({}),
      importKey: jest.fn().mockResolvedValue({})
    }
  }
})

// Mock file system operations
jest.mock('fs/promises', () => ({
  writeFile: jest.fn().mockResolvedValue(undefined),
  readFile: jest.fn().mockResolvedValue('mock-file-content'),
  unlink: jest.fn().mockResolvedValue(undefined),
  mkdir: jest.fn().mockResolvedValue(undefined)
}))

// Mock CSV parser
jest.mock('csv-parse', () => ({
  parse: jest.fn().mockReturnValue([
    {
      studentId: '123456789001',
      firstName: 'John',
      lastName: 'Doe',
      gradeLevel: 'GRADE_11'
    }
  ])
}))

// Mock Excel parser
jest.mock('xlsx', () => ({
  read: jest.fn().mockReturnValue({
    SheetNames: ['Students'],
    Sheets: {
      Students: {}
    }
  }),
  utils: {
    sheet_to_json: jest.fn().mockReturnValue([
      {
        studentId: '123456789001',
        firstName: 'John',
        lastName: 'Doe',
        gradeLevel: 'GRADE_11'
      }
    ])
  }
}))

// ============================================================================
// GLOBAL TEST UTILITIES
// ============================================================================

// Global test data
global.testData = {
  mockSession: {
    user: {
      id: 'test-user-123',
      email: '<EMAIL>',
      name: 'Test Admin',
      role: 'ADMIN',
      permissions: ['students:read', 'students:write', 'students:delete']
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  },
  
  mockStudent: {
    id: 'student-123',
    studentId: '123456789012',
    firstName: 'Juan',
    lastName: 'Dela Cruz',
    middleName: 'Santos',
    email: '<EMAIL>',
    gradeLevel: 'GRADE_11',
    section: 'STEM-A',
    course: 'Science, Technology, Engineering and Mathematics',
    year: '2nd Year',
    status: 'ACTIVE',
    guardianName: 'Maria Dela Cruz',
    guardianPhone: '09123456789',
    guardianRelationship: 'MOTHER',
    municipality: 'Tanauan',
    province: 'Leyte',
    enrollmentDate: new Date('2023-08-15'),
    createdAt: new Date('2023-08-15'),
    updatedAt: new Date('2024-01-15')
  }
}

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
})

// ============================================================================
// CUSTOM MATCHERS
// ============================================================================

// Custom matcher for validating student objects
expect.extend({
  toBeValidStudent(received) {
    const requiredFields = [
      'id', 'studentId', 'firstName', 'lastName', 
      'gradeLevel', 'status', 'createdAt', 'updatedAt'
    ]
    
    const missingFields = requiredFields.filter(field => 
      received[field] === undefined || received[field] === null
    )
    
    if (missingFields.length > 0) {
      return {
        message: () => `Expected student object to have all required fields. Missing: ${missingFields.join(', ')}`,
        pass: false
      }
    }
    
    // Validate DepEd ID format
    if (!/^\d{12}$/.test(received.studentId)) {
      return {
        message: () => `Expected studentId to be 12 digits, got: ${received.studentId}`,
        pass: false
      }
    }
    
    // Validate grade level
    const validGrades = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12']
    if (!validGrades.includes(received.gradeLevel)) {
      return {
        message: () => `Expected gradeLevel to be one of ${validGrades.join(', ')}, got: ${received.gradeLevel}`,
        pass: false
      }
    }
    
    // Validate status
    const validStatuses = ['ACTIVE', 'INACTIVE', 'TRANSFERRED', 'GRADUATED', 'DROPPED']
    if (!validStatuses.includes(received.status)) {
      return {
        message: () => `Expected status to be one of ${validStatuses.join(', ')}, got: ${received.status}`,
        pass: false
      }
    }
    
    return {
      message: () => 'Student object is valid',
      pass: true
    }
  },
  
  toBeValidPagination(received) {
    const requiredFields = ['page', 'limit', 'total', 'totalPages', 'hasNext', 'hasPrev']
    
    const missingFields = requiredFields.filter(field => 
      received[field] === undefined || received[field] === null
    )
    
    if (missingFields.length > 0) {
      return {
        message: () => `Expected pagination object to have all required fields. Missing: ${missingFields.join(', ')}`,
        pass: false
      }
    }
    
    // Validate numeric fields
    const numericFields = ['page', 'limit', 'total', 'totalPages']
    for (const field of numericFields) {
      if (typeof received[field] !== 'number' || received[field] < 0) {
        return {
          message: () => `Expected ${field} to be a non-negative number, got: ${received[field]}`,
          pass: false
        }
      }
    }
    
    // Validate boolean fields
    const booleanFields = ['hasNext', 'hasPrev']
    for (const field of booleanFields) {
      if (typeof received[field] !== 'boolean') {
        return {
          message: () => `Expected ${field} to be a boolean, got: ${received[field]}`,
          pass: false
        }
      }
    }
    
    // Validate logical consistency
    if (received.page > received.totalPages && received.totalPages > 0) {
      return {
        message: () => `Page ${received.page} cannot be greater than totalPages ${received.totalPages}`,
        pass: false
      }
    }
    
    return {
      message: () => 'Pagination object is valid',
      pass: true
    }
  },
  
  toBeValidApiResponse(received) {
    if (typeof received !== 'object' || received === null) {
      return {
        message: () => 'Expected response to be an object',
        pass: false
      }
    }
    
    if (typeof received.success !== 'boolean') {
      return {
        message: () => `Expected success to be a boolean, got: ${typeof received.success}`,
        pass: false
      }
    }
    
    if (!received.success && !received.error) {
      return {
        message: () => 'Expected error message when success is false',
        pass: false
      }
    }
    
    return {
      message: () => 'API response is valid',
      pass: true
    }
  }
})

// ============================================================================
// CONSOLE OVERRIDES FOR CLEANER TEST OUTPUT
// ============================================================================

// Store original console methods
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
  info: console.info
}

// Override console methods to reduce noise during tests
if (process.env.NODE_ENV === 'test') {
  console.log = jest.fn()
  console.info = jest.fn()
  console.warn = jest.fn()
  
  // Keep error logging for debugging
  console.error = (...args) => {
    if (args[0] && args[0].includes && args[0].includes('Warning:')) {
      return // Suppress React warnings
    }
    originalConsole.error(...args)
  }
}

// ============================================================================
// CLEANUP UTILITIES
// ============================================================================

// Global cleanup function
global.cleanup = async () => {
  // Reset all mocks
  jest.clearAllMocks()
  jest.resetAllMocks()
  
  // Clear any test data
  if (global.testData) {
    global.testData = {}
  }
}

// Run cleanup after each test
afterEach(async () => {
  await global.cleanup()
})

// ============================================================================
// TEST PERFORMANCE MONITORING
// ============================================================================

// Track slow tests
const SLOW_TEST_THRESHOLD = 5000 // 5 seconds

beforeEach(() => {
  global.testStartTime = Date.now()
})

afterEach(() => {
  const duration = Date.now() - global.testStartTime
  if (duration > SLOW_TEST_THRESHOLD) {
    console.warn(`⚠️  Slow test detected: ${expect.getState().currentTestName} took ${duration}ms`)
  }
})

console.log('🧪 Jest setup complete - Student Management API Tests ready')
