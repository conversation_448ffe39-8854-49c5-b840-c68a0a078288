import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  qrValidateSchema,
  type QRValidateData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type QRValidateResponse 
} from '@/lib/types/api'
import { qrValidator } from '@/lib/utils/qr-code'

// POST /api/students/qr-validate - Validate QR code data
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    
    // Validate QR validation data
    const validation = qrValidateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid QR validation data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const { qrData, options = {} } = validation.data

    // Parse QR data
    let parsedQRData: any
    try {
      if (typeof qrData === 'string') {
        // Try to decode base64 first (encrypted format)
        try {
          const decoded = Buffer.from(qrData, 'base64').toString('utf8')
          parsedQRData = JSON.parse(decoded)
        } catch {
          // If that fails, try parsing directly (JSON format)
          parsedQRData = JSON.parse(qrData)
        }
      } else {
        parsedQRData = qrData
      }
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Invalid QR code format',
        details: 'Unable to parse QR code data'
      } as ApiResponse, { status: 400 })
    }

    // Validate QR data structure
    const qrValidationResult = qrValidator.validateQRData(parsedQRData)
    if (!qrValidationResult.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Invalid QR code structure',
        details: qrValidationResult.errors
      } as ApiResponse, { status: 400 })
    }

    // Extract student information from QR data
    const studentId = parsedQRData.studentId || parsedQRData.id
    if (!studentId) {
      return NextResponse.json({
        success: false,
        error: 'Student ID not found in QR code'
      } as ApiResponse, { status: 400 })
    }

    // Find student in database
    const student = await prisma.student.findFirst({
      where: {
        OR: [
          { id: studentId },
          { studentId: studentId }
        ]
      }
    })

    if (!student) {
      return NextResponse.json({
        success: false,
        error: 'Student not found',
        details: `No student found with ID: ${studentId}`
      } as ApiResponse, { status: 404 })
    }

    // Check if student is active
    if (student.status !== 'ACTIVE') {
      return NextResponse.json({
        success: false,
        error: 'Student is not active',
        details: `Student status: ${student.status}`,
        data: {
          student: {
            id: student.id,
            studentId: student.studentId,
            name: `${student.firstName} ${student.lastName}`,
            status: student.status
          }
        }
      } as QRValidateResponse, { status: 400 })
    }

    // Check QR code expiration
    const isExpired = parsedQRData.validUntil && new Date(parsedQRData.validUntil) < new Date()
    if (isExpired) {
      return NextResponse.json({
        success: false,
        error: 'QR code has expired',
        details: `QR code expired on: ${parsedQRData.validUntil}`,
        data: {
          student: {
            id: student.id,
            studentId: student.studentId,
            name: `${student.firstName} ${student.lastName}`,
            status: student.status
          },
          qrInfo: {
            isExpired: true,
            expiresAt: parsedQRData.validUntil,
            generatedAt: parsedQRData.generated
          }
        }
      } as QRValidateResponse, { status: 400 })
    }

    // Verify QR data integrity
    const legacyStudent = convertPrismaStudentToLegacy(student as any)
    const integrityCheck = qrValidator.verifyIntegrity(parsedQRData, legacyStudent)
    
    if (!integrityCheck.isValid) {
      return NextResponse.json({
        success: false,
        error: 'QR code data integrity check failed',
        details: integrityCheck.errors,
        data: {
          student: {
            id: student.id,
            studentId: student.studentId,
            name: `${student.firstName} ${student.lastName}`,
            status: student.status
          }
        }
      } as QRValidateResponse, { status: 400 })
    }

    // Check for attendance context if provided
    let attendanceContext = null
    if (options.checkAttendance) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const existingAttendance = await prisma.attendanceRecord.findFirst({
        where: {
          studentId: student.id,
          date: {
            gte: today,
            lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)
          }
        }
      })

      attendanceContext = {
        hasAttendanceToday: !!existingAttendance,
        attendanceStatus: existingAttendance?.status,
        timeIn: existingAttendance?.timeIn,
        timeOut: existingAttendance?.timeOut
      }
    }

    // Log QR validation for audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'QR_VALIDATE',
        entityType: 'Student',
        entityId: student.id,
        changes: JSON.stringify({
          qrValidation: 'success',
          studentId: student.studentId,
          checkAttendance: options.checkAttendance,
          attendanceContext
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        student: {
          id: student.id,
          studentId: student.studentId,
          firstName: student.firstName,
          middleName: student.middleName,
          lastName: student.lastName,
          grade: student.gradeLevel.replace('GRADE_', ''),
          section: student.section,
          course: student.course,
          status: student.status,
          photoUrl: student.photoUrl
        },
        qrInfo: {
          isValid: true,
          isExpired: false,
          expiresAt: parsedQRData.validUntil,
          generatedAt: parsedQRData.generated,
          validatedAt: new Date().toISOString()
        },
        attendanceContext
      },
      message: 'QR code validated successfully'
    } as QRValidateResponse, { status: 200 })

  } catch (error) {
    console.error('QR validation error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// GET /api/students/qr-validate - Get QR validation statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')

    // Get QR validation statistics for the last N days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const validationLogs = await prisma.auditLog.findMany({
      where: {
        action: 'QR_VALIDATE',
        entityType: 'Student',
        createdAt: { gte: startDate }
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            username: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 100
    })

    // Group validations by date
    const validationsByDate = validationLogs.reduce((acc, log) => {
      const date = log.createdAt.toISOString().split('T')[0]
      if (!acc[date]) {
        acc[date] = { total: 0, successful: 0, failed: 0 }
      }
      acc[date].total++
      
      const changes = JSON.parse(log.changes || '{}')
      if (changes.qrValidation === 'success') {
        acc[date].successful++
      } else {
        acc[date].failed++
      }
      
      return acc
    }, {} as Record<string, any>)

    // Get most validated students
    const studentValidations = validationLogs.reduce((acc, log) => {
      const studentId = log.entityId
      if (!acc[studentId]) {
        acc[studentId] = 0
      }
      acc[studentId]++
      return acc
    }, {} as Record<string, number>)

    const topValidatedStudentIds = Object.entries(studentValidations)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([id]) => id)

    const topValidatedStudents = await prisma.student.findMany({
      where: { id: { in: topValidatedStudentIds } },
      select: {
        id: true,
        studentId: true,
        firstName: true,
        lastName: true,
        gradeLevel: true,
        section: true
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        period: {
          days,
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString()
        },
        summary: {
          totalValidations: validationLogs.length,
          uniqueStudents: Object.keys(studentValidations).length,
          averagePerDay: validationLogs.length / days
        },
        validationsByDate,
        topValidatedStudents: topValidatedStudents.map(student => ({
          ...student,
          validationCount: studentValidations[student.id] || 0
        })),
        recentValidations: validationLogs.slice(0, 20).map(log => ({
          id: log.id,
          validatedAt: log.createdAt,
          validatedBy: log.user ? `${log.user.firstName} ${log.user.lastName}` : 'Unknown',
          studentId: log.entityId,
          details: JSON.parse(log.changes || '{}')
        }))
      },
      message: 'QR validation statistics retrieved successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('QR validation statistics error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
