import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  bulkImportSchema,
  createStudentSchema,
  type BulkImportData,
  type CreateStudentData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type BulkImportResponse 
} from '@/lib/types/api'

// POST /api/students/bulk-import - CSV import functionality
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.create')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    
    // Validate bulk import data
    const validation = bulkImportSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid bulk import data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const { students, options = {} } = validation.data
    const { skipDuplicates = false, updateExisting = false, validateOnly = false } = options

    // Initialize counters and error tracking
    let imported = 0
    let updated = 0
    let skipped = 0
    let failed = 0
    const errors: Array<{
      row: number
      studentId?: string
      field?: string
      message: string
      data?: any
    }> = []
    const warnings: Array<{
      row: number
      studentId?: string
      message: string
    }> = []

    // Validate all students first
    const validatedStudents: Array<{ row: number; data: CreateStudentData; isValid: boolean }> = []
    
    for (let i = 0; i < students.length; i++) {
      const student = students[i]
      const row = i + 1
      
      // Validate individual student data
      const studentValidation = createStudentSchema.safeParse(student)
      if (!studentValidation.success) {
        failed++
        studentValidation.error.errors.forEach(error => {
          errors.push({
            row,
            studentId: student.studentId,
            field: error.path.join('.'),
            message: error.message,
            data: student
          })
        })
        validatedStudents.push({ row, data: student as any, isValid: false })
        continue
      }

      validatedStudents.push({ row, data: studentValidation.data, isValid: true })
    }

    // If validation only, return results without importing
    if (validateOnly) {
      return NextResponse.json({
        success: true,
        results: {
          imported: 0,
          updated: 0,
          skipped: 0,
          failed,
          total: students.length
        },
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        message: 'Validation completed'
      } as BulkImportResponse, { status: 200 })
    }

    // Check for existing students
    const studentIds = validatedStudents
      .filter(s => s.isValid)
      .map(s => s.data.studentId)
    
    const existingStudents = await prisma.student.findMany({
      where: { studentId: { in: studentIds } },
      select: { studentId: true, id: true, status: true }
    })
    
    const existingStudentIds = new Set(existingStudents.map(s => s.studentId))

    // Check for email conflicts
    const emails = validatedStudents
      .filter(s => s.isValid && s.data.email)
      .map(s => s.data.email!)
    
    const existingEmails = await prisma.student.findMany({
      where: { 
        email: { in: emails },
        status: { not: 'DROPPED' }
      },
      select: { email: true, studentId: true }
    })
    
    const existingEmailMap = new Map(existingEmails.map(s => [s.email, s.studentId]))

    // Process students in transaction
    const results = await prisma.$transaction(async (tx) => {
      const processedStudents = []
      
      for (const { row, data, isValid } of validatedStudents) {
        if (!isValid) continue

        try {
          // Check for duplicates
          if (existingStudentIds.has(data.studentId)) {
            if (updateExisting) {
              // Update existing student
              const updatedStudent = await tx.student.update({
                where: { studentId: data.studentId },
                data: {
                  firstName: data.firstName,
                  middleName: data.middleName,
                  lastName: data.lastName,
                  email: data.email,
                  dateOfBirth: data.dateOfBirth,
                  gender: data.gender,
                  gradeLevel: data.gradeLevel,
                  section: data.section,
                  course: data.course,
                  year: data.year,
                  status: data.status || 'ACTIVE',
                  guardianName: data.guardianName,
                  guardianPhone: data.guardianPhone,
                  guardianEmail: data.guardianEmail,
                  guardianRelationship: data.guardianRelationship,
                  emergencyContactName: data.emergencyContactName,
                  emergencyContactPhone: data.emergencyContactPhone,
                  emergencyContactRelationship: data.emergencyContactRelationship,
                  address: data.address,
                  barangay: data.barangay,
                  municipality: data.municipality || 'Tanauan',
                  province: data.province || 'Leyte',
                  zipCode: data.zipCode,
                  photoUrl: data.photoUrl,
                  updatedAt: new Date()
                }
              })
              processedStudents.push(updatedStudent)
              updated++
            } else if (skipDuplicates) {
              skipped++
              warnings.push({
                row,
                studentId: data.studentId,
                message: 'Student ID already exists - skipped'
              })
            } else {
              failed++
              errors.push({
                row,
                studentId: data.studentId,
                message: 'Student ID already exists',
                data
              })
            }
            continue
          }

          // Check for email conflicts
          if (data.email && existingEmailMap.has(data.email)) {
            const conflictingStudentId = existingEmailMap.get(data.email)
            if (conflictingStudentId !== data.studentId) {
              failed++
              errors.push({
                row,
                studentId: data.studentId,
                field: 'email',
                message: `Email already exists for student ${conflictingStudentId}`,
                data
              })
              continue
            }
          }

          // Create new student
          const newStudent = await tx.student.create({
            data: {
              studentId: data.studentId,
              firstName: data.firstName,
              middleName: data.middleName,
              lastName: data.lastName,
              email: data.email,
              dateOfBirth: data.dateOfBirth,
              gender: data.gender,
              gradeLevel: data.gradeLevel,
              section: data.section,
              course: data.course,
              year: data.year,
              status: data.status || 'ACTIVE',
              guardianName: data.guardianName,
              guardianPhone: data.guardianPhone,
              guardianEmail: data.guardianEmail,
              guardianRelationship: data.guardianRelationship,
              emergencyContactName: data.emergencyContactName,
              emergencyContactPhone: data.emergencyContactPhone,
              emergencyContactRelationship: data.emergencyContactRelationship,
              address: data.address,
              barangay: data.barangay,
              municipality: data.municipality || 'Tanauan',
              province: data.province || 'Leyte',
              zipCode: data.zipCode,
              photoUrl: data.photoUrl
            }
          })
          
          processedStudents.push(newStudent)
          imported++

        } catch (error) {
          failed++
          errors.push({
            row,
            studentId: data.studentId,
            message: error instanceof Error ? error.message : 'Unknown error occurred',
            data
          })
        }
      }

      return processedStudents
    })

    // Log audit trail for bulk import
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'BULK_IMPORT',
        entityType: 'Student',
        entityId: 'bulk',
        changes: JSON.stringify({
          imported,
          updated,
          skipped,
          failed,
          total: students.length,
          options
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      results: {
        imported,
        updated,
        skipped,
        failed,
        total: students.length
      },
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
      message: `Bulk import completed: ${imported} imported, ${updated} updated, ${skipped} skipped, ${failed} failed`
    } as BulkImportResponse, { status: 200 })

  } catch (error) {
    console.error('Bulk import error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
