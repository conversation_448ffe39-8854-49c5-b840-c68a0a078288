// Global Setup for Student Management API Tests
// Runs once before all tests start

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

module.exports = async () => {
  console.log('🚀 Setting up test environment...')
  
  try {
    // Set test environment variables
    process.env.NODE_ENV = 'test'
    process.env.DATABASE_URL = 'file:./test.db'
    process.env.NEXTAUTH_SECRET = 'test-secret-key-for-testing'
    process.env.NEXTAUTH_URL = 'http://localhost:3000'
    
    // Create test directories
    const testDirs = [
      'test-results',
      'coverage',
      '__tests__/temp',
      'uploads/test'
    ]
    
    testDirs.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir)
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true })
        console.log(`📁 Created test directory: ${dir}`)
      }
    })
    
    // Clean up any existing test database
    const testDbPath = path.join(process.cwd(), 'test.db')
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath)
      console.log('🗑️  Removed existing test database')
    }
    
    // Initialize test database with Prisma
    console.log('🔧 Initializing test database...')
    try {
      // Generate Prisma client
      execSync('npx prisma generate', { 
        stdio: 'pipe',
        env: { ...process.env, DATABASE_URL: 'file:./test.db' }
      })
      
      // Push database schema
      execSync('npx prisma db push --force-reset', { 
        stdio: 'pipe',
        env: { ...process.env, DATABASE_URL: 'file:./test.db' }
      })
      
      console.log('✅ Test database initialized successfully')
    } catch (error) {
      console.warn('⚠️  Database setup failed, tests will use mocked data:', error.message)
    }
    
    // Create test data directory structure
    const testDataDirs = [
      '__tests__/fixtures',
      '__tests__/snapshots',
      '__tests__/temp/uploads',
      '__tests__/temp/exports'
    ]
    
    testDataDirs.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir)
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true })
      }
    })
    
    // Create test fixtures
    const fixtures = {
      'valid-students.json': [
        {
          studentId: '123456789001',
          firstName: 'Test',
          lastName: 'Student1',
          gradeLevel: 'GRADE_11',
          course: 'STEM',
          year: '2nd Year',
          guardianName: 'Test Guardian',
          guardianPhone: '09123456789',
          guardianRelationship: 'FATHER'
        }
      ],
      'invalid-students.json': [
        {
          studentId: '12345', // Invalid - too short
          firstName: 'Invalid',
          lastName: 'Student'
        }
      ],
      'bulk-import-sample.csv': `studentId,firstName,lastName,gradeLevel,course,year,guardianName,guardianPhone,guardianRelationship
123456789001,John,Doe,GRADE_11,STEM,2nd Year,Jane Doe,09123456789,MOTHER
123456789002,Jane,Smith,GRADE_12,ABM,3rd Year,John Smith,09123456790,FATHER`,
      'test-qr-data.json': {
        id: 'test-qr-123',
        studentId: '123456789001',
        name: 'Test Student',
        grade: 'GRADE_11',
        section: 'STEM-A',
        school: 'Test School',
        type: 'student_id',
        generated: new Date().toISOString(),
        version: '1.0'
      }
    }
    
    Object.entries(fixtures).forEach(([filename, content]) => {
      const filePath = path.join(process.cwd(), '__tests__/fixtures', filename)
      const fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2)
      fs.writeFileSync(filePath, fileContent)
    })
    
    console.log('📄 Created test fixtures')
    
    // Set up test performance monitoring
    global.testMetrics = {
      startTime: Date.now(),
      slowTests: [],
      failedTests: [],
      totalTests: 0
    }
    
    // Create test configuration
    const testConfig = {
      database: {
        url: process.env.DATABASE_URL,
        resetBetweenTests: true
      },
      performance: {
        slowTestThreshold: 5000,
        timeoutThreshold: 30000
      },
      coverage: {
        threshold: 80,
        enforceThreshold: false
      },
      cleanup: {
        removeTestFiles: true,
        removeTestDatabase: true
      }
    }
    
    fs.writeFileSync(
      path.join(process.cwd(), '__tests__/test-config.json'),
      JSON.stringify(testConfig, null, 2)
    )
    
    // Log setup completion
    console.log('✅ Test environment setup complete')
    console.log(`📊 Test database: ${process.env.DATABASE_URL}`)
    console.log(`🔧 Node environment: ${process.env.NODE_ENV}`)
    console.log(`⏱️  Setup completed in ${Date.now() - global.testMetrics.startTime}ms`)
    
  } catch (error) {
    console.error('❌ Test setup failed:', error)
    throw error
  }
}
