import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  studentQuerySchema,
  type StudentQueryData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type PaginatedResponse 
} from '@/lib/types/api'
import { GradeLevel } from '@/lib/generated/prisma'

// GET /api/students/grade/[level] - Students by grade level (7-12)
export async function GET(
  request: NextRequest,
  { params }: { params: { level: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Validate grade level
    const gradeLevel = `GRADE_${params.level}` as GradeLevel
    const validGradeLevels = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12']
    
    if (!validGradeLevels.includes(gradeLevel)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid grade level. Must be between 7-12'
      } as ApiResponse, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const queryData: Partial<StudentQueryData> = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: Math.min(parseInt(searchParams.get('limit') || '20'), 100),
      search: searchParams.get('search') || undefined,
      section: searchParams.getAll('section'),
      status: searchParams.getAll('status') as any[],
      course: searchParams.getAll('course'),
      year: searchParams.getAll('year'),
      sortBy: (searchParams.get('sortBy') as any) || 'lastName',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
      include: searchParams.getAll('include') as any[]
    }

    const skip = (queryData.page! - 1) * queryData.limit!

    // Build where clause
    const where: any = {
      gradeLevel: gradeLevel
    }
    
    // Search across multiple fields
    if (queryData.search) {
      where.OR = [
        { firstName: { contains: queryData.search, mode: 'insensitive' } },
        { lastName: { contains: queryData.search, mode: 'insensitive' } },
        { middleName: { contains: queryData.search, mode: 'insensitive' } },
        { studentId: { contains: queryData.search, mode: 'insensitive' } },
        { email: { contains: queryData.search, mode: 'insensitive' } },
        { course: { contains: queryData.search, mode: 'insensitive' } },
        { section: { contains: queryData.search, mode: 'insensitive' } }
      ]
    }

    // Apply additional filters
    if (queryData.section?.length) {
      where.section = { in: queryData.section }
    }
    
    if (queryData.status?.length) {
      where.status = { in: queryData.status }
    }
    
    if (queryData.course?.length) {
      where.course = { in: queryData.course }
    }
    
    if (queryData.year?.length) {
      where.year = { in: queryData.year }
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (queryData.sortBy === 'name') {
      orderBy.lastName = queryData.sortOrder
    } else {
      orderBy[queryData.sortBy!] = queryData.sortOrder
    }

    // Build include clause
    const include: any = {}
    if (queryData.include?.includes('attendanceStats')) {
      include.attendanceRecords = {
        select: {
          id: true,
          date: true,
          status: true,
          timeIn: true,
          timeOut: true
        },
        orderBy: { date: 'desc' },
        take: 30
      }
    }

    // Execute queries
    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        include,
        orderBy,
        skip,
        take: queryData.limit
      }),
      prisma.student.count({ where })
    ])

    // Transform students to legacy format
    const transformedStudents = students.map(student => {
      const legacyStudent = convertPrismaStudentToLegacy(student as any)
      
      // Add attendance stats if requested
      if (queryData.include?.includes('attendanceStats') && student.attendanceRecords) {
        const records = student.attendanceRecords as any[]
        const totalDays = records.length
        const presentDays = records.filter(r => r.status === 'PRESENT').length
        const lateDays = records.filter(r => r.status === 'LATE').length
        const absentDays = records.filter(r => r.status === 'ABSENT').length
        
        legacyStudent.attendanceStats = {
          totalDays,
          presentDays,
          lateDays,
          absentDays,
          attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0
        }
      }
      
      return legacyStudent
    })

    const totalPages = Math.ceil(total / queryData.limit!)

    // Get grade-specific statistics
    const gradeStats = await prisma.student.groupBy({
      by: ['section', 'status'],
      where: { gradeLevel },
      _count: true
    })

    const sectionCounts = gradeStats.reduce((acc, stat) => {
      if (!acc[stat.section || 'No Section']) {
        acc[stat.section || 'No Section'] = { total: 0, active: 0, inactive: 0 }
      }
      acc[stat.section || 'No Section'].total += stat._count
      if (stat.status === 'ACTIVE') {
        acc[stat.section || 'No Section'].active += stat._count
      } else {
        acc[stat.section || 'No Section'].inactive += stat._count
      }
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      success: true,
      data: transformedStudents,
      pagination: {
        page: queryData.page!,
        limit: queryData.limit!,
        total,
        totalPages,
        hasNext: queryData.page! < totalPages,
        hasPrev: queryData.page! > 1
      },
      meta: {
        gradeLevel: params.level,
        totalFiltered: total,
        searchQuery: queryData.search,
        filters: {
          section: queryData.section,
          status: queryData.status,
          course: queryData.course,
          year: queryData.year
        },
        sortBy: queryData.sortBy,
        sortOrder: queryData.sortOrder,
        sectionStats: sectionCounts
      }
    } as PaginatedResponse<any>, { status: 200 })

  } catch (error) {
    console.error('Get students by grade error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
