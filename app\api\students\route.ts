import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  createStudentSchema, 
  studentQuerySchema,
  type CreateStudentData,
  type StudentQueryData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy,
  type StudentWithComputed 
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type PaginatedResponse,
  type StudentApiResponse 
} from '@/lib/types/api'
import { GradeLevel, StudentStatus } from '@/lib/generated/prisma'

// GET /api/students - List all students with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    
    // Parse and validate query parameters
    const queryData: StudentQueryData = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: Math.min(parseInt(searchParams.get('limit') || '20'), 100), // Max 100 per page
      search: searchParams.get('search') || undefined,
      gradeLevel: searchParams.getAll('gradeLevel') as GradeLevel[],
      section: searchParams.getAll('section'),
      status: searchParams.getAll('status') as StudentStatus[],
      course: searchParams.getAll('course'),
      year: searchParams.getAll('year'),
      sortBy: (searchParams.get('sortBy') as any) || 'lastName',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
      include: searchParams.getAll('include') as any[]
    }

    // Validate query parameters
    const validation = studentQuerySchema.safeParse(queryData)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid query parameters',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const validatedQuery = validation.data
    const skip = (validatedQuery.page - 1) * validatedQuery.limit

    // Build where clause for filtering
    const where: any = {}
    
    // Search across multiple fields
    if (validatedQuery.search) {
      where.OR = [
        { firstName: { contains: validatedQuery.search, mode: 'insensitive' } },
        { lastName: { contains: validatedQuery.search, mode: 'insensitive' } },
        { middleName: { contains: validatedQuery.search, mode: 'insensitive' } },
        { studentId: { contains: validatedQuery.search, mode: 'insensitive' } },
        { email: { contains: validatedQuery.search, mode: 'insensitive' } },
        { course: { contains: validatedQuery.search, mode: 'insensitive' } },
        { section: { contains: validatedQuery.search, mode: 'insensitive' } }
      ]
    }

    // Apply filters
    if (validatedQuery.gradeLevel?.length) {
      where.gradeLevel = { in: validatedQuery.gradeLevel }
    }
    
    if (validatedQuery.section?.length) {
      where.section = { in: validatedQuery.section }
    }
    
    if (validatedQuery.status?.length) {
      where.status = { in: validatedQuery.status }
    }
    
    if (validatedQuery.course?.length) {
      where.course = { in: validatedQuery.course }
    }
    
    if (validatedQuery.year?.length) {
      where.year = { in: validatedQuery.year }
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (validatedQuery.sortBy === 'name') {
      orderBy.lastName = validatedQuery.sortOrder
    } else {
      orderBy[validatedQuery.sortBy] = validatedQuery.sortOrder
    }

    // Build include clause
    const include: any = {}
    if (validatedQuery.include?.includes('attendanceStats')) {
      include.attendanceRecords = {
        select: {
          id: true,
          date: true,
          status: true,
          timeIn: true,
          timeOut: true
        },
        orderBy: { date: 'desc' },
        take: 30 // Last 30 attendance records for stats
      }
    }

    // Execute queries
    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        include,
        orderBy,
        skip,
        take: validatedQuery.limit
      }),
      prisma.student.count({ where })
    ])

    // Transform students to legacy format with computed fields
    const transformedStudents = students.map(student => {
      const legacyStudent = convertPrismaStudentToLegacy(student as any)
      
      // Add attendance stats if requested
      if (validatedQuery.include?.includes('attendanceStats') && student.attendanceRecords) {
        const records = student.attendanceRecords as any[]
        const totalDays = records.length
        const presentDays = records.filter(r => r.status === 'PRESENT').length
        const lateDays = records.filter(r => r.status === 'LATE').length
        const absentDays = records.filter(r => r.status === 'ABSENT').length
        
        legacyStudent.attendanceStats = {
          totalDays,
          presentDays,
          lateDays,
          absentDays,
          attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0
        }
      }
      
      return legacyStudent
    })

    const totalPages = Math.ceil(total / validatedQuery.limit)

    return NextResponse.json({
      success: true,
      data: transformedStudents,
      pagination: {
        page: validatedQuery.page,
        limit: validatedQuery.limit,
        total,
        totalPages,
        hasNext: validatedQuery.page < totalPages,
        hasPrev: validatedQuery.page > 1
      },
      meta: {
        totalFiltered: total,
        searchQuery: validatedQuery.search,
        filters: {
          gradeLevel: validatedQuery.gradeLevel,
          section: validatedQuery.section,
          status: validatedQuery.status,
          course: validatedQuery.course,
          year: validatedQuery.year
        },
        sortBy: validatedQuery.sortBy,
        sortOrder: validatedQuery.sortOrder
      }
    } as PaginatedResponse<any>, { status: 200 })

  } catch (error) {
    console.error('Get students error:', error)

    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/students - Create new student record
export async function POST(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.create')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()

    // Validate request data
    const validation = createStudentSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid student data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const studentData = validation.data

    // Check if student ID already exists
    const existingStudent = await prisma.student.findUnique({
      where: { studentId: studentData.studentId }
    })

    if (existingStudent) {
      return NextResponse.json({
        success: false,
        error: 'Student ID already exists'
      } as ApiResponse, { status: 409 })
    }

    // Check if email already exists (if provided)
    if (studentData.email) {
      const existingEmail = await prisma.student.findFirst({
        where: {
          email: studentData.email,
          status: { not: 'DROPPED' } // Allow reuse of email from dropped students
        }
      })

      if (existingEmail) {
        return NextResponse.json({
          success: false,
          error: 'Email already exists'
        } as ApiResponse, { status: 409 })
      }
    }

    // Create student record
    const newStudent = await prisma.student.create({
      data: {
        studentId: studentData.studentId,
        firstName: studentData.firstName,
        middleName: studentData.middleName,
        lastName: studentData.lastName,
        email: studentData.email,
        dateOfBirth: studentData.dateOfBirth,
        gender: studentData.gender,
        gradeLevel: studentData.gradeLevel,
        section: studentData.section,
        course: studentData.course,
        year: studentData.year,
        status: studentData.status || 'ACTIVE',
        guardianName: studentData.guardianName,
        guardianPhone: studentData.guardianPhone,
        guardianEmail: studentData.guardianEmail,
        guardianRelationship: studentData.guardianRelationship,
        emergencyContactName: studentData.emergencyContactName,
        emergencyContactPhone: studentData.emergencyContactPhone,
        emergencyContactRelationship: studentData.emergencyContactRelationship,
        address: studentData.address,
        barangay: studentData.barangay,
        municipality: studentData.municipality || 'Tanauan',
        province: studentData.province || 'Leyte',
        zipCode: studentData.zipCode,
        photoUrl: studentData.photoUrl
      }
    })

    // Convert to legacy format
    const legacyStudent = convertPrismaStudentToLegacy(newStudent as any)

    return NextResponse.json({
      success: true,
      data: legacyStudent,
      message: 'Student created successfully'
    } as StudentApiResponse, { status: 201 })

  } catch (error) {
    console.error('Create student error:', error)

    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/students - Create new student record
export async function POST(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.create')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()

    // Validate request data
    const validation = createStudentSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid student data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const studentData = validation.data

    // Check if student ID already exists
    const existingStudent = await prisma.student.findUnique({
      where: { studentId: studentData.studentId }
    })

    if (existingStudent) {
      return NextResponse.json({
        success: false,
        error: 'Student ID already exists'
      } as ApiResponse, { status: 409 })
    }

    // Check if email already exists (if provided)
    if (studentData.email) {
      const existingEmail = await prisma.student.findFirst({
        where: {
          email: studentData.email,
          status: { not: 'DROPPED' } // Allow reuse of email from dropped students
        }
      })

      if (existingEmail) {
        return NextResponse.json({
          success: false,
          error: 'Email already exists'
        } as ApiResponse, { status: 409 })
      }
    }

    // Create student record
    const newStudent = await prisma.student.create({
      data: {
        studentId: studentData.studentId,
        firstName: studentData.firstName,
        middleName: studentData.middleName,
        lastName: studentData.lastName,
        email: studentData.email,
        dateOfBirth: studentData.dateOfBirth,
        gender: studentData.gender,
        gradeLevel: studentData.gradeLevel,
        section: studentData.section,
        course: studentData.course,
        year: studentData.year,
        status: studentData.status || 'ACTIVE',
        guardianName: studentData.guardianName,
        guardianPhone: studentData.guardianPhone,
        guardianEmail: studentData.guardianEmail,
        guardianRelationship: studentData.guardianRelationship,
        emergencyContactName: studentData.emergencyContactName,
        emergencyContactPhone: studentData.emergencyContactPhone,
        emergencyContactRelationship: studentData.emergencyContactRelationship,
        address: studentData.address,
        barangay: studentData.barangay,
        municipality: studentData.municipality || 'Tanauan',
        province: studentData.province || 'Leyte',
        zipCode: studentData.zipCode,
        photoUrl: studentData.photoUrl
      }
    })

    // Convert to legacy format
    const legacyStudent = convertPrismaStudentToLegacy(newStudent as any)

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'CREATE',
        entityType: 'Student',
        entityId: newStudent.id,
        changes: JSON.stringify({
          created: {
            studentId: newStudent.studentId,
            name: `${newStudent.firstName} ${newStudent.lastName}`,
            gradeLevel: newStudent.gradeLevel
          }
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      data: legacyStudent,
      message: 'Student created successfully'
    } as StudentApiResponse, { status: 201 })

  } catch (error) {
    console.error('Create student error:', error)

    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
