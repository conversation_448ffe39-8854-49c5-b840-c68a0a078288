#!/usr/bin/env node

// Comprehensive Test Runner for Student Management API
// Validates all endpoints, authentication, and business logic

const { execSync, spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// Test configuration
const config = {
  testTimeout: 30000,
  coverageThreshold: 80,
  maxRetries: 3,
  parallelTests: true,
  generateReports: true
}

// Utility functions
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

const logSection = (title) => {
  log(`\n${'='.repeat(60)}`, 'cyan')
  log(`${title}`, 'bright')
  log(`${'='.repeat(60)}`, 'cyan')
}

const logStep = (step, status = 'info') => {
  const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : status === 'warning' ? '⚠️' : 'ℹ️'
  const color = status === 'success' ? 'green' : status === 'error' ? 'red' : status === 'warning' ? 'yellow' : 'blue'
  log(`${icon} ${step}`, color)
}

const execCommand = (command, options = {}) => {
  try {
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    })
    return { success: true, output: result }
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout }
  }
}

// Pre-flight checks
const runPreflightChecks = () => {
  logSection('PRE-FLIGHT CHECKS')
  
  // Check Node.js version
  const nodeVersion = process.version
  logStep(`Node.js version: ${nodeVersion}`)
  
  // Check if package.json exists
  if (!fs.existsSync('package.json')) {
    logStep('package.json not found', 'error')
    process.exit(1)
  }
  logStep('package.json found', 'success')
  
  // Check if test files exist
  const testFiles = [
    '__tests__/api/students.test.ts',
    '__tests__/api/students-integration.test.ts',
    '__tests__/utils/test-helpers.ts'
  ]
  
  let missingFiles = []
  testFiles.forEach(file => {
    if (fs.existsSync(file)) {
      logStep(`Test file found: ${file}`, 'success')
    } else {
      logStep(`Test file missing: ${file}`, 'error')
      missingFiles.push(file)
    }
  })
  
  if (missingFiles.length > 0) {
    logStep(`Missing ${missingFiles.length} test files`, 'error')
    process.exit(1)
  }
  
  // Check Jest configuration
  if (fs.existsSync('jest.config.js')) {
    logStep('Jest configuration found', 'success')
  } else {
    logStep('Jest configuration missing', 'warning')
  }
  
  // Check test dependencies
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const testDeps = ['jest', '@jest/globals', '@types/jest']
  
  testDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      logStep(`Test dependency found: ${dep}`, 'success')
    } else {
      logStep(`Test dependency missing: ${dep}`, 'warning')
    }
  })
  
  logStep('Pre-flight checks completed', 'success')
}

// Install dependencies
const installDependencies = () => {
  logSection('DEPENDENCY INSTALLATION')
  
  logStep('Installing dependencies...')
  const result = execCommand('npm install', { silent: true })
  
  if (result.success) {
    logStep('Dependencies installed successfully', 'success')
  } else {
    logStep('Failed to install dependencies', 'error')
    log(result.error, 'red')
    process.exit(1)
  }
}

// Setup test environment
const setupTestEnvironment = () => {
  logSection('TEST ENVIRONMENT SETUP')
  
  // Create test directories
  const testDirs = [
    '__tests__/temp',
    'test-results',
    'coverage'
  ]
  
  testDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
      logStep(`Created directory: ${dir}`, 'success')
    } else {
      logStep(`Directory exists: ${dir}`, 'success')
    }
  })
  
  // Generate Prisma client
  logStep('Generating Prisma client...')
  const prismaResult = execCommand('npx prisma generate', { silent: true })
  
  if (prismaResult.success) {
    logStep('Prisma client generated', 'success')
  } else {
    logStep('Failed to generate Prisma client', 'warning')
  }
  
  logStep('Test environment setup completed', 'success')
}

// Run unit tests
const runUnitTests = () => {
  logSection('UNIT TESTS')
  
  logStep('Running unit tests...')
  const result = execCommand('npm run test:unit', { silent: false })
  
  if (result.success) {
    logStep('Unit tests passed', 'success')
    return true
  } else {
    logStep('Unit tests failed', 'error')
    return false
  }
}

// Run integration tests
const runIntegrationTests = () => {
  logSection('INTEGRATION TESTS')
  
  logStep('Running integration tests...')
  const result = execCommand('npm run test:integration', { silent: false })
  
  if (result.success) {
    logStep('Integration tests passed', 'success')
    return true
  } else {
    logStep('Integration tests failed', 'error')
    return false
  }
}

// Run coverage analysis
const runCoverageAnalysis = () => {
  logSection('COVERAGE ANALYSIS')
  
  logStep('Running coverage analysis...')
  const result = execCommand('npm run test:coverage', { silent: true })
  
  if (result.success) {
    logStep('Coverage analysis completed', 'success')
    
    // Parse coverage results
    const coveragePath = path.join(process.cwd(), 'coverage', 'coverage-summary.json')
    if (fs.existsSync(coveragePath)) {
      try {
        const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'))
        const total = coverage.total
        
        logStep(`Lines: ${total.lines.pct}%`, total.lines.pct >= config.coverageThreshold ? 'success' : 'warning')
        logStep(`Functions: ${total.functions.pct}%`, total.functions.pct >= config.coverageThreshold ? 'success' : 'warning')
        logStep(`Branches: ${total.branches.pct}%`, total.branches.pct >= config.coverageThreshold ? 'success' : 'warning')
        logStep(`Statements: ${total.statements.pct}%`, total.statements.pct >= config.coverageThreshold ? 'success' : 'warning')
        
        const meetsThreshold = total.lines.pct >= config.coverageThreshold && 
                              total.functions.pct >= config.coverageThreshold && 
                              total.branches.pct >= config.coverageThreshold && 
                              total.statements.pct >= config.coverageThreshold
        
        if (meetsThreshold) {
          logStep(`Coverage threshold (${config.coverageThreshold}%) met`, 'success')
          return true
        } else {
          logStep(`Coverage below threshold (${config.coverageThreshold}%)`, 'warning')
          return false
        }
        
      } catch (error) {
        logStep('Failed to parse coverage results', 'warning')
        return false
      }
    } else {
      logStep('Coverage summary not found', 'warning')
      return false
    }
  } else {
    logStep('Coverage analysis failed', 'error')
    return false
  }
}

// Generate test reports
const generateReports = () => {
  logSection('REPORT GENERATION')
  
  // Generate HTML coverage report
  if (fs.existsSync('coverage/lcov-report/index.html')) {
    logStep('HTML coverage report generated', 'success')
    logStep(`Report location: coverage/lcov-report/index.html`)
  }
  
  // Generate test results
  if (fs.existsSync('test-results')) {
    logStep('Test results directory created', 'success')
  }
  
  // Create summary report
  const summary = {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    testResults: {
      unit: 'completed',
      integration: 'completed',
      coverage: 'completed'
    },
    status: 'completed'
  }
  
  fs.writeFileSync('test-results/summary.json', JSON.stringify(summary, null, 2))
  logStep('Test summary generated', 'success')
}

// Cleanup
const cleanup = () => {
  logSection('CLEANUP')
  
  // Remove temporary files
  const tempFiles = [
    'test.db',
    'test.db-journal',
    'test.db-wal',
    'test.db-shm'
  ]
  
  tempFiles.forEach(file => {
    if (fs.existsSync(file)) {
      try {
        fs.unlinkSync(file)
        logStep(`Removed: ${file}`, 'success')
      } catch (error) {
        logStep(`Failed to remove: ${file}`, 'warning')
      }
    }
  })
  
  logStep('Cleanup completed', 'success')
}

// Main execution
const main = async () => {
  const startTime = Date.now()
  
  log('\n🧪 QRSAMS Student Management API Test Runner', 'bright')
  log('Testing comprehensive student management endpoints\n', 'cyan')
  
  try {
    // Run all test phases
    runPreflightChecks()
    installDependencies()
    setupTestEnvironment()
    
    const unitTestsPass = runUnitTests()
    const integrationTestsPass = runIntegrationTests()
    const coveragePass = runCoverageAnalysis()
    
    if (config.generateReports) {
      generateReports()
    }
    
    cleanup()
    
    // Final results
    logSection('TEST RESULTS SUMMARY')
    
    const totalTime = Date.now() - startTime
    logStep(`Total execution time: ${Math.round(totalTime / 1000)}s`)
    
    logStep(`Unit Tests: ${unitTestsPass ? 'PASSED' : 'FAILED'}`, unitTestsPass ? 'success' : 'error')
    logStep(`Integration Tests: ${integrationTestsPass ? 'PASSED' : 'FAILED'}`, integrationTestsPass ? 'success' : 'error')
    logStep(`Coverage Analysis: ${coveragePass ? 'PASSED' : 'FAILED'}`, coveragePass ? 'success' : 'warning')
    
    const allTestsPass = unitTestsPass && integrationTestsPass
    
    if (allTestsPass) {
      log('\n🎉 ALL TESTS PASSED! Student Management API is ready for production.', 'green')
      process.exit(0)
    } else {
      log('\n❌ SOME TESTS FAILED! Please review the results above.', 'red')
      process.exit(1)
    }
    
  } catch (error) {
    logStep(`Test runner failed: ${error.message}`, 'error')
    process.exit(1)
  }
}

// Handle command line arguments
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  log('QRSAMS Student Management API Test Runner', 'bright')
  log('\nUsage: node scripts/test-runner.js [options]', 'cyan')
  log('\nOptions:')
  log('  --help, -h     Show this help message')
  log('  --no-coverage  Skip coverage analysis')
  log('  --no-reports   Skip report generation')
  log('  --unit-only    Run only unit tests')
  log('  --integration-only  Run only integration tests')
  process.exit(0)
}

// Configure based on arguments
if (args.includes('--no-coverage')) {
  config.generateCoverage = false
}
if (args.includes('--no-reports')) {
  config.generateReports = false
}

// Run the test suite
main().catch(error => {
  log(`Fatal error: ${error.message}`, 'red')
  process.exit(1)
})
