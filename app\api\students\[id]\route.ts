import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  updateStudentSchema,
  type UpdateStudentData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy,
  type StudentWithComputed 
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type StudentApiResponse 
} from '@/lib/types/api'

// GET /api/students/[id] - Get specific student details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const include = searchParams.getAll('include')

    // Build include clause
    const includeClause: any = {}
    if (include.includes('attendanceStats')) {
      includeClause.attendanceRecords = {
        select: {
          id: true,
          date: true,
          status: true,
          timeIn: true,
          timeOut: true
        },
        orderBy: { date: 'desc' },
        take: 30
      }
    }
    if (include.includes('recentAttendance')) {
      includeClause.attendanceRecords = {
        ...includeClause.attendanceRecords,
        take: 10
      }
    }

    const student = await prisma.student.findUnique({
      where: { id: params.id },
      include: includeClause
    })

    if (!student) {
      return NextResponse.json({
        success: false,
        error: 'Student not found'
      } as ApiResponse, { status: 404 })
    }

    // Convert to legacy format
    const legacyStudent = convertPrismaStudentToLegacy(student as any)

    // Add attendance stats if requested
    if (include.includes('attendanceStats') && student.attendanceRecords) {
      const records = student.attendanceRecords as any[]
      const totalDays = records.length
      const presentDays = records.filter(r => r.status === 'PRESENT').length
      const lateDays = records.filter(r => r.status === 'LATE').length
      const absentDays = records.filter(r => r.status === 'ABSENT').length
      
      legacyStudent.attendanceStats = {
        totalDays,
        presentDays,
        lateDays,
        absentDays,
        attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0
      }
    }

    return NextResponse.json({
      success: true,
      data: legacyStudent,
      message: 'Student retrieved successfully'
    } as StudentApiResponse, { status: 200 })

  } catch (error) {
    console.error('Get student error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// PUT /api/students/[id] - Update student information
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.update')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id: params.id }
    })

    if (!existingStudent) {
      return NextResponse.json({
        success: false,
        error: 'Student not found'
      } as ApiResponse, { status: 404 })
    }

    const body = await request.json()
    
    // Validate request data
    const validation = updateStudentSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid student data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const updateData = validation.data

    // Check for conflicts if updating student ID
    if (updateData.studentId && updateData.studentId !== existingStudent.studentId) {
      const conflictingStudent = await prisma.student.findUnique({
        where: { studentId: updateData.studentId }
      })

      if (conflictingStudent) {
        return NextResponse.json({
          success: false,
          error: 'Student ID already exists'
        } as ApiResponse, { status: 409 })
      }
    }

    // Check for email conflicts if updating email
    if (updateData.email && updateData.email !== existingStudent.email) {
      const conflictingEmail = await prisma.student.findFirst({
        where: { 
          email: updateData.email,
          id: { not: params.id },
          status: { not: 'DROPPED' }
        }
      })

      if (conflictingEmail) {
        return NextResponse.json({
          success: false,
          error: 'Email already exists'
        } as ApiResponse, { status: 409 })
      }
    }

    // Update student record
    const updatedStudent = await prisma.student.update({
      where: { id: params.id },
      data: {
        ...updateData,
        updatedAt: new Date()
      }
    })

    // Convert to legacy format
    const legacyStudent = convertPrismaStudentToLegacy(updatedStudent as any)

    return NextResponse.json({
      success: true,
      data: legacyStudent,
      message: 'Student updated successfully'
    } as StudentApiResponse, { status: 200 })

  } catch (error) {
    console.error('Update student error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// DELETE /api/students/[id] - Soft delete student record
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.delete')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id: params.id }
    })

    if (!existingStudent) {
      return NextResponse.json({
        success: false,
        error: 'Student not found'
      } as ApiResponse, { status: 404 })
    }

    // Soft delete by updating status
    const deletedStudent = await prisma.student.update({
      where: { id: params.id },
      data: {
        status: 'DROPPED',
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Student deleted successfully'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Delete student error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
