// API Validation Schemas for Student Management
import { z } from 'zod'

// DepEd ID validation (12-digit format)
export const depEdIdSchema = z.string()
  .length(12, "DepEd ID must be exactly 12 digits")
  .regex(/^\d{12}$/, "DepEd ID must contain only numbers")

// Philippine phone number validation
export const phoneNumberSchema = z.string()
  .min(10, "Phone number must be at least 10 digits")
  .regex(/^(\+63|0)?[0-9\s\-\(\)]+$/, "Invalid Philippine phone number format")
  .transform(val => val.replace(/\D/g, '')) // Remove non-digits
  .refine(val => val.length >= 10 && val.length <= 13, "Phone number must be 10-13 digits")

// Email validation (optional but must be valid if provided)
export const optionalEmailSchema = z.string()
  .email("Invalid email format")
  .optional()
  .or(z.literal(''))

// Grade level validation
export const gradeLevelSchema = z.enum([
  'GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12'
], {
  errorMap: () => ({ message: "Grade level must be between 7-12" })
})

// Student status validation
export const studentStatusSchema = z.enum([
  'ACTIVE', 'INACTIVE', 'TRANSFERRED', 'GRADUATED', 'DROPPED'
])

// Gender validation
export const genderSchema = z.enum(['MALE', 'FEMALE']).optional()

// Guardian relationship validation
export const guardianRelationshipSchema = z.enum([
  'FATHER', 'MOTHER', 'GUARDIAN', 'GRANDPARENT', 'SIBLING', 'OTHER'
])

// Create student request validation
export const createStudentSchema = z.object({
  studentId: depEdIdSchema,
  firstName: z.string()
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must not exceed 50 characters")
    .regex(/^[a-zA-Z\s\-\.]+$/, "First name can only contain letters, spaces, hyphens, and periods"),
  
  lastName: z.string()
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must not exceed 50 characters")
    .regex(/^[a-zA-Z\s\-\.]+$/, "Last name can only contain letters, spaces, hyphens, and periods"),
  
  middleName: z.string()
    .max(50, "Middle name must not exceed 50 characters")
    .regex(/^[a-zA-Z\s\-\.]*$/, "Middle name can only contain letters, spaces, hyphens, and periods")
    .optional(),
  
  email: optionalEmailSchema,
  
  dateOfBirth: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date of birth must be in YYYY-MM-DD format")
    .refine(date => {
      const birthDate = new Date(date)
      const today = new Date()
      const age = today.getFullYear() - birthDate.getFullYear()
      return age >= 10 && age <= 25
    }, "Student must be between 10-25 years old")
    .optional(),
  
  gender: genderSchema,
  gradeLevel: gradeLevelSchema,
  
  section: z.string()
    .max(20, "Section must not exceed 20 characters")
    .regex(/^[a-zA-Z0-9\-]+$/, "Section can only contain letters, numbers, and hyphens")
    .optional(),
  
  course: z.string()
    .min(2, "Course is required")
    .max(100, "Course must not exceed 100 characters"),
  
  year: z.string()
    .min(1, "Year is required")
    .max(20, "Year must not exceed 20 characters"),
  
  // Contact information
  phoneNumber: phoneNumberSchema.optional(),
  guardianName: z.string()
    .min(2, "Guardian name is required")
    .max(100, "Guardian name must not exceed 100 characters"),
  
  guardianPhone: phoneNumberSchema,
  guardianEmail: optionalEmailSchema,
  guardianRelationship: guardianRelationshipSchema,
  
  // Address
  address: z.string().max(200, "Address must not exceed 200 characters").optional(),
  barangay: z.string().max(50, "Barangay must not exceed 50 characters").optional(),
  municipality: z.string().max(50, "Municipality must not exceed 50 characters").optional(),
  province: z.string().max(50, "Province must not exceed 50 characters").optional(),
  zipCode: z.string().max(10, "Zip code must not exceed 10 characters").optional(),
  
  // Emergency contacts
  emergencyContactName: z.string().max(100, "Emergency contact name must not exceed 100 characters").optional(),
  emergencyContactPhone: phoneNumberSchema.optional(),
  emergencyContactRelationship: z.string().max(50, "Emergency contact relationship must not exceed 50 characters").optional(),
  
  // Additional info
  photo: z.string().url("Photo must be a valid URL").optional(),
  medicalInfo: z.string().max(500, "Medical info must not exceed 500 characters").optional(),
  notes: z.string().max(1000, "Notes must not exceed 1000 characters").optional()
})

// Update student request validation (all fields optional except ID)
export const updateStudentSchema = createStudentSchema.partial().extend({
  status: studentStatusSchema.optional()
})

// Query parameters validation
export const studentQuerySchema = z.object({
  page: z.coerce.number().min(1, "Page must be at least 1").default(1),
  limit: z.coerce.number().min(1, "Limit must be at least 1").max(100, "Limit cannot exceed 100").default(20),
  
  // Filters
  search: z.string().max(100, "Search query must not exceed 100 characters").optional(),
  gradeLevel: z.array(gradeLevelSchema).optional(),
  section: z.array(z.string()).optional(),
  status: z.array(studentStatusSchema).optional(),
  course: z.array(z.string()).optional(),
  year: z.array(z.string()).optional(),
  gender: z.array(genderSchema).optional(),
  municipality: z.array(z.string()).optional(),
  guardianPhone: z.string().optional(),
  hasPhoto: z.coerce.boolean().optional(),
  hasQrCode: z.coerce.boolean().optional(),
  enrollmentDateFrom: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  enrollmentDateTo: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  
  // Sorting
  sortBy: z.enum(['firstName', 'lastName', 'studentId', 'gradeLevel', 'section', 'enrollmentDate', 'lastUpdated']).default('lastName'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  
  // Include options
  include: z.array(z.enum(['attendanceStats', 'recentAttendance', 'riskAssessment', 'qrCode'])).optional()
})

// Bulk import validation
export const bulkImportSchema = z.object({
  students: z.array(createStudentSchema).min(1, "At least one student is required").max(1000, "Cannot import more than 1000 students at once"),
  options: z.object({
    skipDuplicates: z.boolean().default(false),
    updateExisting: z.boolean().default(false),
    validateOnly: z.boolean().default(false)
  }).optional()
})

// Bulk update validation
export const bulkUpdateSchema = z.object({
  studentIds: z.array(z.string().cuid()).min(1, "At least one student ID is required").max(100, "Cannot update more than 100 students at once"),
  updates: updateStudentSchema.omit({ studentId: true }),
  options: z.object({
    skipValidation: z.boolean().default(false),
    validateOnly: z.boolean().default(false)
  }).optional()
})

// Bulk delete validation
export const bulkDeleteSchema = z.object({
  studentIds: z.array(z.string().cuid()).min(1, "At least one student ID is required").max(100, "Cannot delete more than 100 students at once"),
  options: z.object({
    hardDelete: z.boolean().default(false),
    validateOnly: z.boolean().default(false),
    reason: z.string().max(200, "Reason must not exceed 200 characters").optional()
  }).optional()
})

// QR code generation validation
export const qrGenerateSchema = z.object({
  studentId: z.string().cuid().optional(),
  studentIds: z.array(z.string().cuid()).max(100, "Cannot generate QR codes for more than 100 students at once").optional(),
  options: z.object({
    format: z.enum(['json', 'encrypted']).default('encrypted'),
    expiresIn: z.number().min(1).max(365).default(365), // days
    includePhoto: z.boolean().default(false),
    size: z.enum(['small', 'medium', 'large']).default('medium')
  }).optional()
}).refine(data => data.studentId || data.studentIds, {
  message: "Either studentId or studentIds must be provided"
})

// QR code validation
export const qrValidateSchema = z.object({
  qrData: z.union([z.string(), z.object({})]).refine(data => {
    if (typeof data === 'string') {
      return data.length > 0
    }
    return Object.keys(data).length > 0
  }, "QR data is required"),
  options: z.object({
    checkAttendance: z.boolean().default(false),
    validateIntegrity: z.boolean().default(true)
  }).optional()
})

// Export validation
export const exportSchema = z.object({
  format: z.enum(['csv', 'excel', 'pdf']),
  filters: studentQuerySchema.omit({ page: true, limit: true, sortBy: true, sortOrder: true, include: true }).optional(),
  fields: z.array(z.string()).optional(),
  options: z.object({
    includePhotos: z.boolean().default(false),
    includeQrCodes: z.boolean().default(false),
    includeAttendanceStats: z.boolean().default(false),
    template: z.string().optional()
  }).optional()
})

// Search validation
export const searchSchema = z.object({
  query: z.string().min(1, "Search query is required").max(100, "Search query must not exceed 100 characters"),
  filters: studentQuerySchema.omit({ page: true, limit: true, sortBy: true, sortOrder: true, include: true, search: true }).optional(),
  options: z.object({
    fuzzy: z.boolean().default(true),
    fields: z.array(z.string()).optional(),
    limit: z.number().min(1).max(50).default(10),
    highlightMatches: z.boolean().default(true)
  }).optional()
})

// Export all schemas
export type CreateStudentData = z.infer<typeof createStudentSchema>
export type UpdateStudentData = z.infer<typeof updateStudentSchema>
export type StudentQueryData = z.infer<typeof studentQuerySchema>
export type BulkImportData = z.infer<typeof bulkImportSchema>
export type BulkUpdateData = z.infer<typeof bulkUpdateSchema>
export type BulkDeleteData = z.infer<typeof bulkDeleteSchema>
export type QRGenerateData = z.infer<typeof qrGenerateSchema>
export type QRValidateData = z.infer<typeof qrValidateSchema>
export type ExportData = z.infer<typeof exportSchema>
export type SearchData = z.infer<typeof searchSchema>

// Validation utility functions
export const validationUtils = {
  // Validate DepEd ID format and check digit
  validateDepEdId: (id: string): { isValid: boolean; error?: string } => {
    if (!id || typeof id !== 'string') {
      return { isValid: false, error: 'DepEd ID is required' }
    }

    if (id.length !== 12) {
      return { isValid: false, error: 'DepEd ID must be exactly 12 digits' }
    }

    if (!/^\d{12}$/.test(id)) {
      return { isValid: false, error: 'DepEd ID must contain only numbers' }
    }

    // Additional validation: check if ID follows DepEd format
    // Format: RRDDSSYYNNNC (Region-Division-School-Year-Number-Check)
    const region = id.substring(0, 2)
    const division = id.substring(2, 4)
    const school = id.substring(4, 6)
    const year = id.substring(6, 8)

    // Basic range checks
    if (parseInt(region) < 1 || parseInt(region) > 17) {
      return { isValid: false, error: 'Invalid region code in DepEd ID' }
    }

    const currentYear = new Date().getFullYear() % 100
    const idYear = parseInt(year)
    if (idYear < currentYear - 10 || idYear > currentYear + 1) {
      return { isValid: false, error: 'Invalid year in DepEd ID' }
    }

    return { isValid: true }
  },

  // Validate Philippine phone number with detailed formatting
  validatePhilippinePhone: (phone: string): { isValid: boolean; formatted?: string; error?: string } => {
    if (!phone || typeof phone !== 'string') {
      return { isValid: false, error: 'Phone number is required' }
    }

    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '')

    // Check length
    if (cleaned.length < 10 || cleaned.length > 13) {
      return { isValid: false, error: 'Phone number must be 10-13 digits' }
    }

    // Check Philippine mobile patterns
    const mobilePatterns = [
      /^(09\d{9})$/, // 09XXXXXXXXX
      /^(639\d{9})$/, // 639XXXXXXXXX
      /^(\+639\d{9})$/ // +639XXXXXXXXX
    ]

    // Check landline patterns (Metro Manila and provinces)
    const landlinePatterns = [
      /^(02\d{7,8})$/, // Metro Manila: 02XXXXXXX or 02XXXXXXXX
      /^(\d{3}\d{7})$/ // Provincial: XXXYYYYYYY
    ]

    let formatted = cleaned
    let isValid = false

    // Format mobile numbers
    if (cleaned.startsWith('09') && cleaned.length === 11) {
      formatted = cleaned
      isValid = true
    } else if (cleaned.startsWith('639') && cleaned.length === 12) {
      formatted = '0' + cleaned.substring(2)
      isValid = true
    } else if (cleaned.startsWith('63') && cleaned.length === 12) {
      formatted = '0' + cleaned.substring(2)
      isValid = true
    }

    // Check landline patterns
    if (!isValid) {
      for (const pattern of landlinePatterns) {
        if (pattern.test(cleaned)) {
          formatted = cleaned
          isValid = true
          break
        }
      }
    }

    if (!isValid) {
      return { isValid: false, error: 'Invalid Philippine phone number format' }
    }

    return { isValid: true, formatted }
  },

  // Validate grade level and section combination
  validateGradeSection: (gradeLevel: string, section?: string): { isValid: boolean; error?: string } => {
    const validGrades = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12']

    if (!validGrades.includes(gradeLevel)) {
      return { isValid: false, error: 'Invalid grade level' }
    }

    if (section) {
      // Section naming conventions
      const sectionPattern = /^[A-Z][A-Z0-9\-]*$/
      if (!sectionPattern.test(section)) {
        return { isValid: false, error: 'Section must start with a letter and contain only uppercase letters, numbers, and hyphens' }
      }

      if (section.length > 20) {
        return { isValid: false, error: 'Section name must not exceed 20 characters' }
      }
    }

    return { isValid: true }
  },

  // Validate student age based on grade level
  validateAgeForGrade: (dateOfBirth: string, gradeLevel: string): { isValid: boolean; error?: string } => {
    const birthDate = new Date(dateOfBirth)
    const today = new Date()
    const age = today.getFullYear() - birthDate.getFullYear()

    // Adjust age if birthday hasn't occurred this year
    if (today.getMonth() < birthDate.getMonth() ||
        (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())) {
      age - 1
    }

    // Expected age ranges for each grade level
    const gradeAgeRanges: Record<string, { min: number; max: number }> = {
      'GRADE_7': { min: 12, max: 15 },
      'GRADE_8': { min: 13, max: 16 },
      'GRADE_9': { min: 14, max: 17 },
      'GRADE_10': { min: 15, max: 18 },
      'GRADE_11': { min: 16, max: 19 },
      'GRADE_12': { min: 17, max: 20 }
    }

    const ageRange = gradeAgeRanges[gradeLevel]
    if (!ageRange) {
      return { isValid: false, error: 'Invalid grade level' }
    }

    if (age < ageRange.min || age > ageRange.max) {
      return {
        isValid: false,
        error: `Age ${age} is outside expected range (${ageRange.min}-${ageRange.max}) for ${gradeLevel.replace('GRADE_', 'Grade ')}`
      }
    }

    return { isValid: true }
  },

  // Validate email domain for school requirements
  validateSchoolEmail: (email: string, allowPersonal: boolean = true): { isValid: boolean; error?: string } => {
    if (!email) {
      return { isValid: true } // Email is optional
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return { isValid: false, error: 'Invalid email format' }
    }

    // School domain validation (if required)
    if (!allowPersonal) {
      const schoolDomains = ['deped.gov.ph', 'tsat.edu.ph'] // Add school-specific domains
      const domain = email.split('@')[1].toLowerCase()

      if (!schoolDomains.includes(domain)) {
        return { isValid: false, error: 'Email must use an approved school domain' }
      }
    }

    return { isValid: true }
  }
}
