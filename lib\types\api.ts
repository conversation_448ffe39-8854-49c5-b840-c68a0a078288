// Student Management API Types
// Comprehensive TypeScript interfaces for QRSAMS student API endpoints

// Base API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp?: string
  requestId?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  meta?: {
    totalFiltered?: number
    searchQuery?: string
    filters?: Record<string, any>
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }
}

// Student API specific types
export interface StudentApiResponse<T = any> extends ApiResponse<T> {
  student?: T
  students?: T[]
}

export interface StudentListResponse extends PaginatedResponse<any> {
  students: any[]
  summary?: {
    totalActive: number
    totalInactive: number
    totalByGrade: Record<string, number>
    totalBySection: Record<string, number>
  }
}

// Request types
export interface CreateStudentRequest {
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  dateOfBirth?: string
  gender?: 'MALE' | 'FEMALE'
  gradeLevel: 'GRADE_7' | 'GRADE_8' | 'GRADE_9' | 'GRADE_10' | 'GRADE_11' | 'GRADE_12'
  section?: string
  course: string
  year: string
  
  // Contact information
  phoneNumber?: string
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: 'FATHER' | 'MOTHER' | 'GUARDIAN' | 'GRANDPARENT' | 'SIBLING' | 'OTHER'
  
  // Address
  address?: string
  barangay?: string
  municipality?: string
  province?: string
  zipCode?: string
  
  // Emergency contacts
  emergencyContactName?: string
  emergencyContactPhone?: string
  emergencyContactRelationship?: string
  
  // Additional info
  photo?: string
  medicalInfo?: string
  notes?: string
}

export interface UpdateStudentRequest extends Partial<CreateStudentRequest> {
  status?: 'ACTIVE' | 'INACTIVE' | 'TRANSFERRED' | 'GRADUATED' | 'DROPPED'
}

export interface StudentFilters {
  search?: string
  gradeLevel?: string[]
  section?: string[]
  status?: string[]
  course?: string[]
  year?: string[]
  gender?: string[]
  municipality?: string[]
  guardianPhone?: string
  hasPhoto?: boolean
  hasQrCode?: boolean
  enrollmentDateFrom?: string
  enrollmentDateTo?: string
}

export interface StudentSortOptions {
  sortBy?: 'firstName' | 'lastName' | 'studentId' | 'gradeLevel' | 'section' | 'enrollmentDate' | 'lastUpdated'
  sortOrder?: 'asc' | 'desc'
}

export interface StudentQueryParams extends StudentFilters, StudentSortOptions {
  page?: number
  limit?: number
  include?: ('attendanceStats' | 'recentAttendance' | 'riskAssessment' | 'qrCode')[]
}

// Bulk operations
export interface BulkImportRequest {
  students: CreateStudentRequest[]
  options?: {
    skipDuplicates?: boolean
    updateExisting?: boolean
    validateOnly?: boolean
  }
}

export interface BulkImportResponse extends ApiResponse {
  results: {
    imported: number
    updated: number
    skipped: number
    failed: number
    total: number
  }
  errors?: Array<{
    row: number
    studentId?: string
    field?: string
    message: string
    data?: any
  }>
  warnings?: Array<{
    row: number
    studentId?: string
    message: string
  }>
}

export interface BulkUpdateRequest {
  studentIds: string[]
  updates: Partial<UpdateStudentRequest>
  options?: {
    skipValidation?: boolean
  }
}

export interface BulkDeleteRequest {
  studentIds: string[]
  options?: {
    hardDelete?: boolean
    reason?: string
  }
}

// QR Code operations
export interface QRGenerateRequest {
  studentId?: string
  studentIds?: string[]
  options?: {
    format?: 'json' | 'encrypted'
    expiresIn?: number // days
    includePhoto?: boolean
    size?: 'small' | 'medium' | 'large'
  }
}

export interface QRGenerateResponse extends ApiResponse {
  qrCode?: string
  qrCodes?: Array<{
    studentId: string
    qrCode: string
    qrData: string
    expiresAt?: string
  }>
  downloadUrl?: string
}

export interface QRValidateRequest {
  qrCode: string
  qrData?: string
}

export interface QRValidateResponse extends ApiResponse {
  valid: boolean
  student?: any
  expiresAt?: string
  generatedAt?: string
  validationErrors?: string[]
}

// Export operations
export interface ExportRequest {
  format: 'csv' | 'excel' | 'pdf'
  filters?: StudentFilters
  fields?: string[]
  options?: {
    includePhotos?: boolean
    includeQrCodes?: boolean
    includeAttendanceStats?: boolean
    template?: string
  }
}

export interface ExportResponse extends ApiResponse {
  downloadUrl: string
  filename: string
  fileSize: number
  recordCount: number
  expiresAt: string
}

// Search operations
export interface SearchRequest {
  query: string
  filters?: StudentFilters
  options?: {
    fuzzy?: boolean
    fields?: string[]
    limit?: number
    highlightMatches?: boolean
  }
}

export interface SearchResponse extends ApiResponse {
  results: Array<{
    student: any
    score: number
    matches?: Array<{
      field: string
      value: string
      highlighted?: string
    }>
  }>
  totalResults: number
  searchTime: number
  suggestions?: string[]
}

// Error types
export interface ValidationError {
  field: string
  message: string
  code: string
  value?: any
}

export interface ApiError extends Error {
  statusCode: number
  code: string
  details?: any
  validationErrors?: ValidationError[]
}

// Enhanced API Response types with better type safety
export interface SuccessResponse<T = any> extends ApiResponse<T> {
  success: true
  data: T
  error?: never
}

export interface ErrorResponse extends ApiResponse<never> {
  success: false
  data?: never
  error: string
  validationErrors?: ValidationError[]
}

export type ApiResult<T = any> = SuccessResponse<T> | ErrorResponse

// Student-specific response types with proper typing
export interface StudentResponse extends SuccessResponse<any> {
  student: any
}

export interface StudentsListResponse extends PaginatedResponse<any> {
  students: any[]
  summary?: {
    totalActive: number
    totalInactive: number
    totalByGrade: Record<string, number>
    totalBySection: Record<string, number>
    totalByStatus: Record<string, number>
  }
}

// Grade-specific response types
export interface GradeStudentsResponse extends PaginatedResponse<any> {
  students: any[]
  gradeLevel: string
  gradeStats?: {
    totalStudents: number
    sections: string[]
    averageAge?: number
  }
}

// Section-specific response types
export interface SectionStudentsResponse extends PaginatedResponse<any> {
  students: any[]
  section: string
  sectionStats?: {
    totalStudents: number
    gradeLevel: string
    averageAge?: number
  }
}

// Attendance statistics interface
export interface AttendanceStats {
  totalDays: number
  presentDays: number
  lateDays: number
  absentDays: number
  attendanceRate: number
  lastAttendance?: string
  recentPattern?: 'improving' | 'declining' | 'stable'
}

// Risk assessment interface
export interface RiskAssessment {
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  score: number
  factors: string[]
  recommendations: string[]
  lastAssessment: string
}

// Enhanced student data with computed properties
export interface EnhancedStudentData {
  id: string
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  fullName: string
  email?: string
  dateOfBirth?: string
  age?: number
  gender?: 'MALE' | 'FEMALE'
  gradeLevel: string
  section?: string
  course: string
  year: string
  status: string

  // Contact information
  phoneNumber?: string
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: string

  // Address
  address?: string
  barangay?: string
  municipality?: string
  province?: string
  zipCode?: string

  // Emergency contacts
  emergencyContactName?: string
  emergencyContactPhone?: string
  emergencyContactRelationship?: string

  // Additional info
  photo?: string
  medicalInfo?: string
  notes?: string
  qrCode?: string

  // Computed properties
  attendanceStats?: AttendanceStats
  riskAssessment?: RiskAssessment

  // Timestamps
  enrollmentDate: string
  createdAt: string
  updatedAt: string
}

// Database operation result types
export interface DatabaseOperationResult<T = any> {
  success: boolean
  data?: T
  error?: string
  affectedRows?: number
  executionTime?: number
}

// Bulk operation result types
export interface BulkOperationResults {
  total: number
  successful: number
  failed: number
  skipped?: number
  updated?: number
  imported?: number
  deleted?: number
}

export interface BulkOperationError {
  row?: number
  id?: string
  field?: string
  message: string
  data?: any
}

export interface BulkOperationWarning {
  row?: number
  id?: string
  message: string
}

// Enhanced bulk responses
export interface EnhancedBulkImportResponse extends ApiResponse {
  results: BulkOperationResults
  errors?: BulkOperationError[]
  warnings?: BulkOperationWarning[]
  processingTime: number
  validationSummary?: {
    totalValidated: number
    validRecords: number
    invalidRecords: number
    duplicates: number
  }
}

export interface BulkUpdateResponse extends ApiResponse {
  results: BulkOperationResults
  errors?: BulkOperationError[]
  updatedFields: string[]
  processingTime: number
}

export interface BulkDeleteResponse extends ApiResponse {
  results: BulkOperationResults
  errors?: BulkOperationError[]
  deletionType: 'soft' | 'hard'
  processingTime: number
}

// QR Code enhanced types
export interface QRCodeMetadata {
  studentId: string
  studentName: string
  gradeLevel: string
  section?: string
  school: string
  generatedAt: string
  expiresAt?: string
  format: 'json' | 'encrypted'
  version: string
}

export interface EnhancedQRGenerateResponse extends ApiResponse {
  qrCode?: string
  qrCodes?: Array<{
    studentId: string
    studentName: string
    qrCode: string
    qrData: string
    metadata: QRCodeMetadata
    downloadUrl?: string
  }>
  batchId?: string
  downloadUrl?: string
  generationTime: number
  totalGenerated: number
}

export interface EnhancedQRValidateResponse extends ApiResponse {
  valid: boolean
  student?: EnhancedStudentData
  qrMetadata?: QRCodeMetadata
  validationDetails: {
    structureValid: boolean
    integrityValid: boolean
    notExpired: boolean
    studentExists: boolean
    dataMatches: boolean
  }
  validationErrors?: string[]
  validationTime: number
}

// Search enhanced types
export interface SearchMatch {
  field: string
  value: string
  highlighted?: string
  relevance: number
}

export interface SearchResult {
  student: EnhancedStudentData
  score: number
  matches: SearchMatch[]
  relevanceFactors: string[]
}

export interface EnhancedSearchResponse extends ApiResponse {
  results: SearchResult[]
  totalResults: number
  searchTime: number
  searchQuery: string
  suggestions?: string[]
  facets?: {
    grades: Record<string, number>
    sections: Record<string, number>
    statuses: Record<string, number>
  }
}

// Export enhanced types
export interface EnhancedExportResponse extends ApiResponse {
  downloadUrl: string
  filename: string
  fileSize: number
  recordCount: number
  format: 'csv' | 'excel' | 'pdf'
  expiresAt: string
  generationTime: number
  includedFields: string[]
  filters?: Record<string, any>
}

// Utility types
export type StudentStatus = 'ACTIVE' | 'INACTIVE' | 'TRANSFERRED' | 'GRADUATED' | 'DROPPED'
export type GradeLevel = 'GRADE_7' | 'GRADE_8' | 'GRADE_9' | 'GRADE_10' | 'GRADE_11' | 'GRADE_12'
export type Gender = 'MALE' | 'FEMALE'
export type GuardianRelationship = 'FATHER' | 'MOTHER' | 'GUARDIAN' | 'GRANDPARENT' | 'SIBLING' | 'OTHER'

// Constants
export const GRADE_LEVELS: GradeLevel[] = [
  'GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12'
]

export const STUDENT_STATUSES: StudentStatus[] = [
  'ACTIVE', 'INACTIVE', 'TRANSFERRED', 'GRADUATED', 'DROPPED'
]

export const GUARDIAN_RELATIONSHIPS: GuardianRelationship[] = [
  'FATHER', 'MOTHER', 'GUARDIAN', 'GRANDPARENT', 'SIBLING', 'OTHER'
]

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 20
export const MAX_PAGE_SIZE = 100
export const DEFAULT_PAGE = 1
