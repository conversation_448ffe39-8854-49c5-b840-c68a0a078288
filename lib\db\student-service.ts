import { prisma } from '../prisma'
import { logAuditEntry } from '../auth/security'
import { validationUtils } from '../validations/api'
import { convertPrismaStudentToLegacy } from '../types/prisma'
import type {
  CreateStudentData,
  UpdateStudentData,
  StudentQueryData,
  BulkImportData,
  BulkUpdateData,
  BulkDeleteData
} from '../validations/api'
import type { GradeLevel, StudentStatus } from '../generated/prisma'

// Enhanced Student Database Service Layer
export class StudentDatabaseService {
  private static instance: StudentDatabaseService
  
  static getInstance(): StudentDatabaseService {
    if (!StudentDatabaseService.instance) {
      StudentDatabaseService.instance = new StudentDatabaseService()
    }
    return StudentDatabaseService.instance
  }

  // Create student with comprehensive validation and audit trail
  async createStudent(
    data: CreateStudentData, 
    userId: string, 
    ipAddress?: string, 
    userAgent?: string
  ) {
    return await prisma.$transaction(async (tx) => {
      // Validate DepEd ID
      const depEdValidation = validationUtils.validateDepEdId(data.studentId)
      if (!depEdValidation.isValid) {
        throw new Error(depEdValidation.error)
      }

      // Check for duplicate student ID
      const existingStudent = await tx.student.findUnique({
        where: { studentId: data.studentId }
      })
      
      if (existingStudent) {
        throw new Error(`Student with ID ${data.studentId} already exists`)
      }

      // Check for duplicate email if provided
      if (data.email) {
        const existingEmail = await tx.student.findFirst({
          where: { 
            email: data.email,
            status: { not: 'DROPPED' }
          }
        })
        
        if (existingEmail) {
          throw new Error(`Email ${data.email} is already in use`)
        }
      }

      // Validate phone numbers
      if (data.guardianPhone) {
        const phoneValidation = validationUtils.validatePhilippinePhone(data.guardianPhone)
        if (!phoneValidation.isValid) {
          throw new Error(`Invalid guardian phone: ${phoneValidation.error}`)
        }
        data.guardianPhone = phoneValidation.formatted
      }

      if (data.emergencyContactPhone) {
        const phoneValidation = validationUtils.validatePhilippinePhone(data.emergencyContactPhone)
        if (!phoneValidation.isValid) {
          throw new Error(`Invalid emergency contact phone: ${phoneValidation.error}`)
        }
        data.emergencyContactPhone = phoneValidation.formatted
      }

      // Validate age for grade level
      if (data.dateOfBirth && data.gradeLevel) {
        const ageValidation = validationUtils.validateAgeForGrade(data.dateOfBirth, data.gradeLevel)
        if (!ageValidation.isValid) {
          throw new Error(ageValidation.error)
        }
      }

      // Create student record
      const student = await tx.student.create({
        data: {
          ...data,
          municipality: data.municipality || 'Tanauan',
          province: data.province || 'Leyte',
          status: data.status || 'ACTIVE',
          enrollmentDate: new Date()
        }
      })

      // Log audit trail
      await logAuditEntry({
        userId,
        action: 'CREATE',
        entityType: 'Student',
        entityId: student.id,
        newValues: { studentId: student.studentId, name: `${student.firstName} ${student.lastName}` },
        ipAddress,
        userAgent
      })

      return convertPrismaStudentToLegacy(student as any)
    })
  }

  // Update student with conflict checking and audit trail
  async updateStudent(
    id: string,
    data: UpdateStudentData,
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ) {
    return await prisma.$transaction(async (tx) => {
      // Get existing student
      const existingStudent = await tx.student.findUnique({
        where: { id }
      })

      if (!existingStudent) {
        throw new Error('Student not found')
      }

      // Check for conflicts if updating student ID
      if (data.studentId && data.studentId !== existingStudent.studentId) {
        const depEdValidation = validationUtils.validateDepEdId(data.studentId)
        if (!depEdValidation.isValid) {
          throw new Error(depEdValidation.error)
        }

        const conflictingStudent = await tx.student.findUnique({
          where: { studentId: data.studentId }
        })
        
        if (conflictingStudent) {
          throw new Error(`Student with ID ${data.studentId} already exists`)
        }
      }

      // Check for email conflicts if updating email
      if (data.email && data.email !== existingStudent.email) {
        const conflictingEmail = await tx.student.findFirst({
          where: { 
            email: data.email,
            status: { not: 'DROPPED' },
            id: { not: id }
          }
        })
        
        if (conflictingEmail) {
          throw new Error(`Email ${data.email} is already in use`)
        }
      }

      // Validate phone numbers if updating
      if (data.guardianPhone) {
        const phoneValidation = validationUtils.validatePhilippinePhone(data.guardianPhone)
        if (!phoneValidation.isValid) {
          throw new Error(`Invalid guardian phone: ${phoneValidation.error}`)
        }
        data.guardianPhone = phoneValidation.formatted
      }

      if (data.emergencyContactPhone) {
        const phoneValidation = validationUtils.validatePhilippinePhone(data.emergencyContactPhone)
        if (!phoneValidation.isValid) {
          throw new Error(`Invalid emergency contact phone: ${phoneValidation.error}`)
        }
        data.emergencyContactPhone = phoneValidation.formatted
      }

      // Update student record
      const updatedStudent = await tx.student.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date()
        }
      })

      // Log audit trail
      await logAuditEntry({
        userId,
        action: 'UPDATE',
        entityType: 'Student',
        entityId: id,
        oldValues: { 
          studentId: existingStudent.studentId, 
          name: `${existingStudent.firstName} ${existingStudent.lastName}`,
          status: existingStudent.status
        },
        newValues: { 
          studentId: updatedStudent.studentId, 
          name: `${updatedStudent.firstName} ${updatedStudent.lastName}`,
          status: updatedStudent.status
        },
        ipAddress,
        userAgent
      })

      return convertPrismaStudentToLegacy(updatedStudent as any)
    })
  }

  // Soft delete student with audit trail
  async deleteStudent(
    id: string,
    userId: string,
    hardDelete: boolean = false,
    ipAddress?: string,
    userAgent?: string
  ) {
    return await prisma.$transaction(async (tx) => {
      const existingStudent = await tx.student.findUnique({
        where: { id },
        include: {
          attendanceRecords: { select: { id: true } }
        }
      })

      if (!existingStudent) {
        throw new Error('Student not found')
      }

      if (hardDelete) {
        // Check if student has attendance records
        if (existingStudent.attendanceRecords.length > 0) {
          throw new Error('Cannot hard delete student with attendance records')
        }

        // Hard delete
        await tx.student.delete({
          where: { id }
        })

        // Log audit trail
        await logAuditEntry({
          userId,
          action: 'HARD_DELETE',
          entityType: 'Student',
          entityId: id,
          oldValues: { 
            studentId: existingStudent.studentId, 
            name: `${existingStudent.firstName} ${existingStudent.lastName}`
          },
          ipAddress,
          userAgent
        })

        return { deleted: true, type: 'hard' }
      } else {
        // Soft delete - update status to DROPPED
        const updatedStudent = await tx.student.update({
          where: { id },
          data: {
            status: 'DROPPED',
            updatedAt: new Date()
          }
        })

        // Log audit trail
        await logAuditEntry({
          userId,
          action: 'SOFT_DELETE',
          entityType: 'Student',
          entityId: id,
          oldValues: { status: existingStudent.status },
          newValues: { status: 'DROPPED' },
          ipAddress,
          userAgent
        })

        return { 
          deleted: true, 
          type: 'soft', 
          student: convertPrismaStudentToLegacy(updatedStudent as any) 
        }
      }
    })
  }

  // Get student with optimized includes
  async getStudent(id: string, includes: string[] = []) {
    const includeOptions: any = {}
    
    if (includes.includes('attendanceStats')) {
      includeOptions.attendanceRecords = {
        select: {
          id: true,
          date: true,
          status: true,
          timeIn: true,
          timeOut: true
        },
        orderBy: { date: 'desc' },
        take: 30
      }
    }

    if (includes.includes('riskAssessment')) {
      includeOptions.riskAssessments = {
        take: 1,
        orderBy: { assessmentDate: 'desc' }
      }
    }

    const student = await prisma.student.findUnique({
      where: { id },
      include: includeOptions
    })

    if (!student) {
      return null
    }

    const legacyStudent = convertPrismaStudentToLegacy(student as any)

    // Calculate attendance stats if requested
    if (includes.includes('attendanceStats') && student.attendanceRecords) {
      const records = student.attendanceRecords as any[]
      const totalDays = records.length
      const presentDays = records.filter(r => r.status === 'PRESENT').length
      const lateDays = records.filter(r => r.status === 'LATE').length
      const absentDays = records.filter(r => r.status === 'ABSENT').length
      
      legacyStudent.attendanceStats = {
        totalDays,
        presentDays,
        lateDays,
        absentDays,
        attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0
      }
    }

    return legacyStudent
  }

  // Optimized student listing with advanced filtering
  async listStudents(query: StudentQueryData) {
    const {
      page = 1,
      limit = 20,
      search,
      gradeLevel,
      section,
      status,
      course,
      year,
      sortBy = 'lastName',
      sortOrder = 'asc',
      include = []
    } = query

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { middleName: { contains: search, mode: 'insensitive' } },
        { studentId: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { course: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (gradeLevel?.length) {
      where.gradeLevel = { in: gradeLevel }
    }
    
    if (section?.length) {
      where.section = { in: section }
    }
    
    if (status?.length) {
      where.status = { in: status }
    }
    
    if (course?.length) {
      where.course = { in: course }
    }
    
    if (year?.length) {
      where.year = { in: year }
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (sortBy === 'name') {
      orderBy.lastName = sortOrder
    } else {
      orderBy[sortBy] = sortOrder
    }

    // Build include clause
    const includeOptions: any = {}
    if (include.includes('attendanceStats')) {
      includeOptions.attendanceRecords = {
        select: {
          id: true,
          date: true,
          status: true,
          timeIn: true,
          timeOut: true
        },
        orderBy: { date: 'desc' },
        take: 30
      }
    }

    // Execute queries
    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        include: includeOptions,
        orderBy,
        skip,
        take: limit
      }),
      prisma.student.count({ where })
    ])

    // Transform students to legacy format
    const transformedStudents = students.map(student => {
      const legacyStudent = convertPrismaStudentToLegacy(student as any)
      
      // Add attendance stats if requested
      if (include.includes('attendanceStats') && student.attendanceRecords) {
        const records = student.attendanceRecords as any[]
        const totalDays = records.length
        const presentDays = records.filter(r => r.status === 'PRESENT').length
        const lateDays = records.filter(r => r.status === 'LATE').length
        const absentDays = records.filter(r => r.status === 'ABSENT').length
        
        legacyStudent.attendanceStats = {
          totalDays,
          presentDays,
          lateDays,
          absentDays,
          attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0
        }
      }
      
      return legacyStudent
    })

    const totalPages = Math.ceil(total / limit)

    return {
      students: transformedStudents,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }
}

  // Bulk import students with comprehensive validation and error handling
  async bulkImportStudents(
    data: BulkImportData,
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ) {
    const { students, options = {} } = data
    const { skipDuplicates = false, updateExisting = false, validateOnly = false } = options

    const results = {
      imported: 0,
      updated: 0,
      skipped: 0,
      failed: 0,
      total: students.length
    }

    const errors: Array<{
      row: number
      studentId?: string
      field?: string
      message: string
      data?: any
    }> = []

    const warnings: Array<{
      row: number
      studentId?: string
      message: string
    }> = []

    return await prisma.$transaction(async (tx) => {
      for (let i = 0; i < students.length; i++) {
        const studentData = students[i]
        const rowNumber = i + 1

        try {
          // Validate DepEd ID
          const depEdValidation = validationUtils.validateDepEdId(studentData.studentId)
          if (!depEdValidation.isValid) {
            errors.push({
              row: rowNumber,
              studentId: studentData.studentId,
              field: 'studentId',
              message: depEdValidation.error!,
              data: studentData
            })
            results.failed++
            continue
          }

          // Check for existing student
          const existingStudent = await tx.student.findUnique({
            where: { studentId: studentData.studentId }
          })

          if (existingStudent) {
            if (existingStudent.status === 'DROPPED') {
              warnings.push({
                row: rowNumber,
                studentId: studentData.studentId,
                message: 'Student was previously dropped'
              })
            }

            if (skipDuplicates) {
              results.skipped++
              continue
            }

            if (updateExisting) {
              if (!validateOnly) {
                await tx.student.update({
                  where: { studentId: studentData.studentId },
                  data: {
                    ...studentData,
                    updatedAt: new Date()
                  }
                })
              }
              results.updated++
            } else {
              errors.push({
                row: rowNumber,
                studentId: studentData.studentId,
                message: 'Student already exists',
                data: studentData
              })
              results.failed++
            }
            continue
          }

          // Validate phone numbers
          if (studentData.guardianPhone) {
            const phoneValidation = validationUtils.validatePhilippinePhone(studentData.guardianPhone)
            if (!phoneValidation.isValid) {
              errors.push({
                row: rowNumber,
                studentId: studentData.studentId,
                field: 'guardianPhone',
                message: phoneValidation.error!,
                data: studentData
              })
              results.failed++
              continue
            }
            studentData.guardianPhone = phoneValidation.formatted
          }

          // Validate age for grade level
          if (studentData.dateOfBirth && studentData.gradeLevel) {
            const ageValidation = validationUtils.validateAgeForGrade(studentData.dateOfBirth, studentData.gradeLevel)
            if (!ageValidation.isValid) {
              warnings.push({
                row: rowNumber,
                studentId: studentData.studentId,
                message: ageValidation.error!
              })
            }
          }

          // Create student if not in validate-only mode
          if (!validateOnly) {
            await tx.student.create({
              data: {
                ...studentData,
                municipality: studentData.municipality || 'Tanauan',
                province: studentData.province || 'Leyte',
                status: studentData.status || 'ACTIVE',
                enrollmentDate: new Date()
              }
            })
          }

          results.imported++

        } catch (error) {
          errors.push({
            row: rowNumber,
            studentId: studentData.studentId,
            message: error instanceof Error ? error.message : 'Unknown error',
            data: studentData
          })
          results.failed++
        }
      }

      // Log audit trail for bulk import
      if (!validateOnly) {
        await logAuditEntry({
          userId,
          action: 'BULK_IMPORT',
          entityType: 'Student',
          entityId: 'bulk',
          newValues: {
            total: results.total,
            imported: results.imported,
            updated: results.updated,
            skipped: results.skipped,
            failed: results.failed,
            options
          },
          ipAddress,
          userAgent
        })
      }

      return {
        results,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined
      }
    })
  }

  // Bulk update students
  async bulkUpdateStudents(
    data: BulkUpdateData,
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ) {
    const { studentIds, updates, options = {} } = data
    const { skipValidation = false } = options

    return await prisma.$transaction(async (tx) => {
      const results = {
        updated: 0,
        failed: 0,
        total: studentIds.length
      }

      const errors: Array<{
        studentId: string
        message: string
      }> = []

      for (const studentId of studentIds) {
        try {
          const existingStudent = await tx.student.findUnique({
            where: { id: studentId }
          })

          if (!existingStudent) {
            errors.push({
              studentId,
              message: 'Student not found'
            })
            results.failed++
            continue
          }

          // Validate updates if not skipping validation
          if (!skipValidation) {
            if (updates.studentId && updates.studentId !== existingStudent.studentId) {
              const depEdValidation = validationUtils.validateDepEdId(updates.studentId)
              if (!depEdValidation.isValid) {
                errors.push({
                  studentId,
                  message: `Invalid DepEd ID: ${depEdValidation.error}`
                })
                results.failed++
                continue
              }

              // Check for conflicts
              const conflictingStudent = await tx.student.findUnique({
                where: { studentId: updates.studentId }
              })

              if (conflictingStudent && conflictingStudent.id !== studentId) {
                errors.push({
                  studentId,
                  message: `Student ID ${updates.studentId} already exists`
                })
                results.failed++
                continue
              }
            }
          }

          await tx.student.update({
            where: { id: studentId },
            data: {
              ...updates,
              updatedAt: new Date()
            }
          })

          results.updated++

        } catch (error) {
          errors.push({
            studentId,
            message: error instanceof Error ? error.message : 'Unknown error'
          })
          results.failed++
        }
      }

      // Log audit trail
      await logAuditEntry({
        userId,
        action: 'BULK_UPDATE',
        entityType: 'Student',
        entityId: 'bulk',
        newValues: {
          studentIds,
          updates,
          results,
          options
        },
        ipAddress,
        userAgent
      })

      return {
        results,
        errors: errors.length > 0 ? errors : undefined
      }
    })
  }

  // Bulk delete students
  async bulkDeleteStudents(
    data: BulkDeleteData,
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ) {
    const { studentIds, options = {} } = data
    const { hardDelete = false, reason } = options

    return await prisma.$transaction(async (tx) => {
      const results = {
        deleted: 0,
        failed: 0,
        total: studentIds.length
      }

      const errors: Array<{
        studentId: string
        message: string
      }> = []

      for (const studentId of studentIds) {
        try {
          const existingStudent = await tx.student.findUnique({
            where: { id: studentId },
            include: {
              attendanceRecords: { select: { id: true } }
            }
          })

          if (!existingStudent) {
            errors.push({
              studentId,
              message: 'Student not found'
            })
            results.failed++
            continue
          }

          if (hardDelete) {
            if (existingStudent.attendanceRecords.length > 0) {
              errors.push({
                studentId,
                message: 'Cannot hard delete student with attendance records'
              })
              results.failed++
              continue
            }

            await tx.student.delete({
              where: { id: studentId }
            })
          } else {
            await tx.student.update({
              where: { id: studentId },
              data: {
                status: 'DROPPED',
                updatedAt: new Date()
              }
            })
          }

          results.deleted++

        } catch (error) {
          errors.push({
            studentId,
            message: error instanceof Error ? error.message : 'Unknown error'
          })
          results.failed++
        }
      }

      // Log audit trail
      await logAuditEntry({
        userId,
        action: hardDelete ? 'BULK_HARD_DELETE' : 'BULK_SOFT_DELETE',
        entityType: 'Student',
        entityId: 'bulk',
        newValues: {
          studentIds,
          results,
          options,
          reason
        },
        ipAddress,
        userAgent
      })

      return {
        results,
        errors: errors.length > 0 ? errors : undefined
      }
    })
  }

  // Get student statistics and summaries
  async getStudentSummary() {
    const [
      totalStudents,
      activeStudents,
      inactiveStudents,
      gradeDistribution,
      statusDistribution
    ] = await Promise.all([
      prisma.student.count(),
      prisma.student.count({ where: { status: 'ACTIVE' } }),
      prisma.student.count({ where: { status: 'INACTIVE' } }),
      prisma.student.groupBy({
        by: ['gradeLevel'],
        _count: { id: true },
        orderBy: { gradeLevel: 'asc' }
      }),
      prisma.student.groupBy({
        by: ['status'],
        _count: { id: true }
      })
    ])

    const totalByGrade = gradeDistribution.reduce((acc, item) => {
      acc[item.gradeLevel] = item._count.id
      return acc
    }, {} as Record<string, number>)

    const totalByStatus = statusDistribution.reduce((acc, item) => {
      acc[item.status] = item._count.id
      return acc
    }, {} as Record<string, number>)

    return {
      totalStudents,
      totalActive: activeStudents,
      totalInactive: inactiveStudents,
      totalByGrade,
      totalByStatus
    }
  }

  // Advanced search with fuzzy matching and relevance scoring
  async searchStudents(query: string, options: {
    fuzzy?: boolean
    fields?: string[]
    limit?: number
    filters?: any
  } = {}) {
    const {
      fuzzy = true,
      fields = ['firstName', 'lastName', 'studentId', 'email'],
      limit = 10,
      filters = {}
    } = options

    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0)

    const where: any = {
      ...filters,
      OR: []
    }

    // Build search conditions
    for (const term of searchTerms) {
      if (fields.includes('firstName')) {
        where.OR.push({ firstName: { contains: term, mode: 'insensitive' } })
      }
      if (fields.includes('lastName')) {
        where.OR.push({ lastName: { contains: term, mode: 'insensitive' } })
      }
      if (fields.includes('studentId')) {
        where.OR.push({ studentId: { contains: term, mode: 'insensitive' } })
      }
      if (fields.includes('email')) {
        where.OR.push({ email: { contains: term, mode: 'insensitive' } })
      }
      if (fields.includes('course')) {
        where.OR.push({ course: { contains: term, mode: 'insensitive' } })
      }
    }

    const students = await prisma.student.findMany({
      where,
      take: limit,
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' }
      ]
    })

    // Calculate relevance scores
    const results = students.map(student => {
      let score = 0
      const matches: Array<{ field: string; value: string }> = []

      const studentText = `${student.firstName} ${student.lastName} ${student.studentId} ${student.email || ''}`.toLowerCase()

      for (const term of searchTerms) {
        if (studentText.includes(term)) {
          score += 1

          // Exact matches get higher scores
          if (student.firstName.toLowerCase() === term || student.lastName.toLowerCase() === term) {
            score += 2
          }
          if (student.studentId.toLowerCase().includes(term)) {
            score += 1.5
          }
        }
      }

      // Add field-specific matches
      if (student.firstName.toLowerCase().includes(query.toLowerCase())) {
        matches.push({ field: 'firstName', value: student.firstName })
      }
      if (student.lastName.toLowerCase().includes(query.toLowerCase())) {
        matches.push({ field: 'lastName', value: student.lastName })
      }
      if (student.studentId.toLowerCase().includes(query.toLowerCase())) {
        matches.push({ field: 'studentId', value: student.studentId })
      }

      return {
        student: convertPrismaStudentToLegacy(student as any),
        score,
        matches
      }
    })

    // Sort by relevance score
    results.sort((a, b) => b.score - a.score)

    return {
      results,
      totalResults: results.length,
      searchTime: Date.now() // This would be calculated properly in a real implementation
    }
  }
}

// Export singleton instance
export const studentDbService = StudentDatabaseService.getInstance()
