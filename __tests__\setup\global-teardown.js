// Global Teardown for Student Management API Tests
// Runs once after all tests complete

const fs = require('fs')
const path = require('path')

module.exports = async () => {
  console.log('🧹 Cleaning up test environment...')
  
  try {
    const startTime = Date.now()
    
    // Read test configuration
    let testConfig = {}
    try {
      const configPath = path.join(process.cwd(), '__tests__/test-config.json')
      if (fs.existsSync(configPath)) {
        testConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'))
      }
    } catch (error) {
      console.warn('⚠️  Could not read test configuration:', error.message)
    }
    
    // Generate test report
    if (global.testMetrics) {
      const totalDuration = Date.now() - global.testMetrics.startTime
      const report = {
        summary: {
          totalTests: global.testMetrics.totalTests,
          totalDuration: totalDuration,
          averageTestTime: global.testMetrics.totalTests > 0 ? totalDuration / global.testMetrics.totalTests : 0,
          slowTests: global.testMetrics.slowTests.length,
          failedTests: global.testMetrics.failedTests.length
        },
        slowTests: global.testMetrics.slowTests,
        failedTests: global.testMetrics.failedTests,
        performance: {
          slowTestThreshold: testConfig.performance?.slowTestThreshold || 5000,
          timeoutThreshold: testConfig.performance?.timeoutThreshold || 30000
        },
        timestamp: new Date().toISOString()
      }
      
      try {
        const reportPath = path.join(process.cwd(), 'test-results', 'test-metrics.json')
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
        console.log('📊 Test metrics report generated')
        
        // Log summary
        console.log(`📈 Test Summary:`)
        console.log(`   Total Tests: ${report.summary.totalTests}`)
        console.log(`   Total Duration: ${report.summary.totalDuration}ms`)
        console.log(`   Average Test Time: ${Math.round(report.summary.averageTestTime)}ms`)
        console.log(`   Slow Tests: ${report.summary.slowTests}`)
        console.log(`   Failed Tests: ${report.summary.failedTests}`)
        
        if (report.summary.slowTests > 0) {
          console.log('⚠️  Slow tests detected:')
          report.slowTests.forEach(test => {
            console.log(`   - ${test.name}: ${test.duration}ms`)
          })
        }
        
      } catch (error) {
        console.warn('⚠️  Could not generate test report:', error.message)
      }
    }
    
    // Clean up test database
    if (testConfig.cleanup?.removeTestDatabase !== false) {
      const testDbPath = path.join(process.cwd(), 'test.db')
      if (fs.existsSync(testDbPath)) {
        try {
          fs.unlinkSync(testDbPath)
          console.log('🗑️  Removed test database')
        } catch (error) {
          console.warn('⚠️  Could not remove test database:', error.message)
        }
      }
      
      // Also remove any SQLite journal files
      const journalFiles = [
        'test.db-journal',
        'test.db-wal',
        'test.db-shm'
      ]
      
      journalFiles.forEach(file => {
        const filePath = path.join(process.cwd(), file)
        if (fs.existsSync(filePath)) {
          try {
            fs.unlinkSync(filePath)
            console.log(`🗑️  Removed ${file}`)
          } catch (error) {
            console.warn(`⚠️  Could not remove ${file}:`, error.message)
          }
        }
      })
    }
    
    // Clean up temporary test files
    if (testConfig.cleanup?.removeTestFiles !== false) {
      const tempDirs = [
        '__tests__/temp',
        'uploads/test'
      ]
      
      tempDirs.forEach(dir => {
        const fullPath = path.join(process.cwd(), dir)
        if (fs.existsSync(fullPath)) {
          try {
            fs.rmSync(fullPath, { recursive: true, force: true })
            console.log(`🗑️  Removed temporary directory: ${dir}`)
          } catch (error) {
            console.warn(`⚠️  Could not remove ${dir}:`, error.message)
          }
        }
      })
    }
    
    // Clean up test fixtures (optional)
    const fixturesPath = path.join(process.cwd(), '__tests__/fixtures')
    if (fs.existsSync(fixturesPath) && testConfig.cleanup?.removeFixtures) {
      try {
        fs.rmSync(fixturesPath, { recursive: true, force: true })
        console.log('🗑️  Removed test fixtures')
      } catch (error) {
        console.warn('⚠️  Could not remove test fixtures:', error.message)
      }
    }
    
    // Clean up test configuration
    const configPath = path.join(process.cwd(), '__tests__/test-config.json')
    if (fs.existsSync(configPath)) {
      try {
        fs.unlinkSync(configPath)
        console.log('🗑️  Removed test configuration')
      } catch (error) {
        console.warn('⚠️  Could not remove test configuration:', error.message)
      }
    }
    
    // Generate coverage summary if available
    const coveragePath = path.join(process.cwd(), 'coverage', 'coverage-summary.json')
    if (fs.existsSync(coveragePath)) {
      try {
        const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'))
        const total = coverage.total
        
        console.log('📊 Coverage Summary:')
        console.log(`   Lines: ${total.lines.pct}%`)
        console.log(`   Functions: ${total.functions.pct}%`)
        console.log(`   Branches: ${total.branches.pct}%`)
        console.log(`   Statements: ${total.statements.pct}%`)
        
        const threshold = testConfig.coverage?.threshold || 80
        const meetsThreshold = total.lines.pct >= threshold && 
                              total.functions.pct >= threshold && 
                              total.branches.pct >= threshold && 
                              total.statements.pct >= threshold
        
        if (meetsThreshold) {
          console.log('✅ Coverage threshold met')
        } else {
          console.log(`⚠️  Coverage below threshold (${threshold}%)`)
          if (testConfig.coverage?.enforceThreshold) {
            console.error('❌ Coverage threshold enforcement enabled - build should fail')
          }
        }
        
      } catch (error) {
        console.warn('⚠️  Could not read coverage summary:', error.message)
      }
    }
    
    // Log cleanup completion
    const cleanupDuration = Date.now() - startTime
    console.log(`✅ Test environment cleanup complete (${cleanupDuration}ms)`)
    
    // Final memory cleanup
    if (global.testMetrics) {
      delete global.testMetrics
    }
    
    if (global.testData) {
      delete global.testData
    }
    
    if (global.cleanup) {
      delete global.cleanup
    }
    
  } catch (error) {
    console.error('❌ Test cleanup failed:', error)
    // Don't throw error to avoid masking test results
  }
}
