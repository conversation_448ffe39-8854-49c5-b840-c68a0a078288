import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  studentQuerySchema,
  type StudentQueryData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type PaginatedResponse 
} from '@/lib/types/api'
import { GradeLevel, StudentStatus } from '@/lib/generated/prisma'

// GET /api/students/section/[section] - Students by section
export async function GET(
  request: NextRequest,
  { params }: { params: { section: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Decode section name (handle URL encoding)
    const sectionName = decodeURIComponent(params.section)

    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const queryData: Partial<StudentQueryData> = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: Math.min(parseInt(searchParams.get('limit') || '20'), 100),
      search: searchParams.get('search') || undefined,
      gradeLevel: searchParams.getAll('gradeLevel') as GradeLevel[],
      status: searchParams.getAll('status') as StudentStatus[],
      course: searchParams.getAll('course'),
      year: searchParams.getAll('year'),
      sortBy: (searchParams.get('sortBy') as any) || 'lastName',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
      include: searchParams.getAll('include') as any[]
    }

    const skip = (queryData.page! - 1) * queryData.limit!

    // Build where clause
    const where: any = {
      section: sectionName
    }
    
    // Search across multiple fields
    if (queryData.search) {
      where.OR = [
        { firstName: { contains: queryData.search, mode: 'insensitive' } },
        { lastName: { contains: queryData.search, mode: 'insensitive' } },
        { middleName: { contains: queryData.search, mode: 'insensitive' } },
        { studentId: { contains: queryData.search, mode: 'insensitive' } },
        { email: { contains: queryData.search, mode: 'insensitive' } },
        { course: { contains: queryData.search, mode: 'insensitive' } }
      ]
    }

    // Apply additional filters
    if (queryData.gradeLevel?.length) {
      where.gradeLevel = { in: queryData.gradeLevel }
    }
    
    if (queryData.status?.length) {
      where.status = { in: queryData.status }
    }
    
    if (queryData.course?.length) {
      where.course = { in: queryData.course }
    }
    
    if (queryData.year?.length) {
      where.year = { in: queryData.year }
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (queryData.sortBy === 'name') {
      orderBy.lastName = queryData.sortOrder
    } else {
      orderBy[queryData.sortBy!] = queryData.sortOrder
    }

    // Build include clause
    const include: any = {}
    if (queryData.include?.includes('attendanceStats')) {
      include.attendanceRecords = {
        select: {
          id: true,
          date: true,
          status: true,
          timeIn: true,
          timeOut: true
        },
        orderBy: { date: 'desc' },
        take: 30
      }
    }

    // Execute queries
    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        include,
        orderBy,
        skip,
        take: queryData.limit
      }),
      prisma.student.count({ where })
    ])

    // Transform students to legacy format
    const transformedStudents = students.map(student => {
      const legacyStudent = convertPrismaStudentToLegacy(student as any)
      
      // Add attendance stats if requested
      if (queryData.include?.includes('attendanceStats') && student.attendanceRecords) {
        const records = student.attendanceRecords as any[]
        const totalDays = records.length
        const presentDays = records.filter(r => r.status === 'PRESENT').length
        const lateDays = records.filter(r => r.status === 'LATE').length
        const absentDays = records.filter(r => r.status === 'ABSENT').length
        
        legacyStudent.attendanceStats = {
          totalDays,
          presentDays,
          lateDays,
          absentDays,
          attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0
        }
      }
      
      return legacyStudent
    })

    const totalPages = Math.ceil(total / queryData.limit!)

    // Get section-specific statistics
    const sectionStats = await prisma.student.groupBy({
      by: ['gradeLevel', 'status'],
      where: { section: sectionName },
      _count: true
    })

    const gradeLevelCounts = sectionStats.reduce((acc, stat) => {
      const grade = stat.gradeLevel.replace('GRADE_', '')
      if (!acc[grade]) {
        acc[grade] = { total: 0, active: 0, inactive: 0 }
      }
      acc[grade].total += stat._count
      if (stat.status === 'ACTIVE') {
        acc[grade].active += stat._count
      } else {
        acc[grade].inactive += stat._count
      }
      return acc
    }, {} as Record<string, any>)

    // Get section schedule/subjects if available
    const sectionSubjects = await prisma.subject.findMany({
      where: {
        sections: {
          has: sectionName
        }
      },
      select: {
        id: true,
        name: true,
        code: true,
        gradeLevel: true,
        teacher: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: transformedStudents,
      pagination: {
        page: queryData.page!,
        limit: queryData.limit!,
        total,
        totalPages,
        hasNext: queryData.page! < totalPages,
        hasPrev: queryData.page! > 1
      },
      meta: {
        section: sectionName,
        totalFiltered: total,
        searchQuery: queryData.search,
        filters: {
          gradeLevel: queryData.gradeLevel,
          status: queryData.status,
          course: queryData.course,
          year: queryData.year
        },
        sortBy: queryData.sortBy,
        sortOrder: queryData.sortOrder,
        gradeLevelStats: gradeLevelCounts,
        subjects: sectionSubjects
      }
    } as PaginatedResponse<any>, { status: 200 })

  } catch (error) {
    console.error('Get students by section error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
