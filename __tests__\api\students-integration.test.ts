// Integration Tests for Student Management API
// Tests the complete flow of student operations with real database interactions

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals'
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  GET as studentsGET, 
  POST as studentsPOST 
} from '@/app/api/students/route'
import { 
  GET as studentGET, 
  PUT as studentPUT, 
  DELETE as studentDELETE 
} from '@/app/api/students/[id]/route'

// Test database setup
const TEST_DATABASE_URL = process.env.TEST_DATABASE_URL || 'file:./test.db'

// Mock authentication for integration tests
jest.mock('@/lib/auth/config', () => ({
  auth: jest.fn().mockResolvedValue({
    user: {
      id: 'test-user-123',
      role: 'ADMIN',
      permissions: ['students:read', 'students:write', 'students:delete']
    }
  })
}))

jest.mock('@/lib/auth/permissions', () => ({
  hasPermission: jest.fn().mockReturnValue(true)
}))

// Helper functions
const createMockRequest = (
  method: string, 
  url: string, 
  body?: any, 
  headers?: Record<string, string>
): NextRequest => {
  return new NextRequest(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    },
    body: body ? JSON.stringify(body) : undefined
  })
}

const createTestStudent = async (overrides: any = {}) => {
  const studentData = {
    studentId: `12345678901${Math.floor(Math.random() * 10)}`,
    firstName: 'Test',
    lastName: 'Student',
    gradeLevel: 'GRADE_11',
    course: 'General Academic Strand',
    year: '2nd Year',
    guardianName: 'Test Guardian',
    guardianPhone: '09123456789',
    guardianRelationship: 'FATHER',
    municipality: 'Tanauan',
    province: 'Leyte',
    status: 'ACTIVE',
    ...overrides
  }

  return await prisma.student.create({
    data: {
      ...studentData,
      enrollmentDate: new Date()
    }
  })
}

const cleanupTestData = async () => {
  // Clean up test data
  await prisma.student.deleteMany({
    where: {
      firstName: 'Test'
    }
  })
}

describe('Student Management API Integration Tests', () => {
  beforeAll(async () => {
    // Ensure test database is clean
    await cleanupTestData()
  })

  afterAll(async () => {
    // Clean up after all tests
    await cleanupTestData()
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Clean up before each test
    await cleanupTestData()
  })

  afterEach(async () => {
    // Clean up after each test
    await cleanupTestData()
  })

  describe('Complete Student Lifecycle', () => {
    it('should create, read, update, and delete a student', async () => {
      // 1. Create a student
      const createData = {
        studentId: '123456789099',
        firstName: 'Integration',
        lastName: 'Test',
        gradeLevel: 'GRADE_10',
        course: 'Science, Technology, Engineering and Mathematics',
        year: '1st Year',
        guardianName: 'Integration Guardian',
        guardianPhone: '09123456789',
        guardianRelationship: 'MOTHER'
      }

      const createRequest = createMockRequest('POST', 'http://localhost:3000/api/students', createData)
      const createResponse = await studentsPOST(createRequest)
      const createResult = await createResponse.json()

      expect(createResponse.status).toBe(201)
      expect(createResult.success).toBe(true)
      expect(createResult.student.studentId).toBe(createData.studentId)

      const studentId = createResult.student.id

      // 2. Read the student
      const readRequest = createMockRequest('GET', `http://localhost:3000/api/students/${studentId}`)
      const readResponse = await studentGET(readRequest, { params: { id: studentId } })
      const readResult = await readResponse.json()

      expect(readResponse.status).toBe(200)
      expect(readResult.success).toBe(true)
      expect(readResult.student.firstName).toBe('Integration')

      // 3. Update the student
      const updateData = {
        firstName: 'Updated Integration',
        section: 'STEM-A'
      }

      const updateRequest = createMockRequest('PUT', `http://localhost:3000/api/students/${studentId}`, updateData)
      const updateResponse = await studentPUT(updateRequest, { params: { id: studentId } })
      const updateResult = await updateResponse.json()

      expect(updateResponse.status).toBe(200)
      expect(updateResult.success).toBe(true)
      expect(updateResult.student.firstName).toBe('Updated Integration')
      expect(updateResult.student.section).toBe('STEM-A')

      // 4. Soft delete the student
      const deleteRequest = createMockRequest('DELETE', `http://localhost:3000/api/students/${studentId}`)
      const deleteResponse = await studentDELETE(deleteRequest, { params: { id: studentId } })
      const deleteResult = await deleteResponse.json()

      expect(deleteResponse.status).toBe(200)
      expect(deleteResult.success).toBe(true)

      // 5. Verify student is soft deleted
      const verifyRequest = createMockRequest('GET', `http://localhost:3000/api/students/${studentId}`)
      const verifyResponse = await studentGET(verifyRequest, { params: { id: studentId } })
      const verifyResult = await verifyResponse.json()

      expect(verifyResponse.status).toBe(200)
      expect(verifyResult.student.status).toBe('DROPPED')
    })
  })

  describe('Data Validation and Constraints', () => {
    it('should enforce unique student ID constraint', async () => {
      const studentData = {
        studentId: '123456789088',
        firstName: 'First',
        lastName: 'Student',
        gradeLevel: 'GRADE_11',
        course: 'General Academic Strand',
        year: '2nd Year',
        guardianName: 'Guardian One',
        guardianPhone: '09123456789',
        guardianRelationship: 'FATHER'
      }

      // Create first student
      const firstRequest = createMockRequest('POST', 'http://localhost:3000/api/students', studentData)
      const firstResponse = await studentsPOST(firstRequest)
      expect(firstResponse.status).toBe(201)

      // Try to create second student with same ID
      const duplicateData = { ...studentData, firstName: 'Second' }
      const secondRequest = createMockRequest('POST', 'http://localhost:3000/api/students', duplicateData)
      const secondResponse = await studentsPOST(secondRequest)
      const secondResult = await secondResponse.json()

      expect(secondResponse.status).toBe(409)
      expect(secondResult.success).toBe(false)
      expect(secondResult.error).toContain('already exists')
    })

    it('should validate DepEd ID format', async () => {
      const invalidData = {
        studentId: '12345', // Too short
        firstName: 'Test',
        lastName: 'Student',
        gradeLevel: 'GRADE_11',
        course: 'General Academic Strand',
        year: '2nd Year',
        guardianName: 'Test Guardian',
        guardianPhone: '09123456789',
        guardianRelationship: 'FATHER'
      }

      const request = createMockRequest('POST', 'http://localhost:3000/api/students', invalidData)
      const response = await studentsPOST(request)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toContain('DepEd ID')
    })

    it('should validate Philippine phone number format', async () => {
      const invalidData = {
        studentId: '123456789077',
        firstName: 'Test',
        lastName: 'Student',
        gradeLevel: 'GRADE_11',
        course: 'General Academic Strand',
        year: '2nd Year',
        guardianName: 'Test Guardian',
        guardianPhone: 'invalid-phone',
        guardianRelationship: 'FATHER'
      }

      const request = createMockRequest('POST', 'http://localhost:3000/api/students', invalidData)
      const response = await studentsPOST(request)
      const result = await response.json()

      expect(response.status).toBe(400)
      expect(result.success).toBe(false)
      expect(result.error).toContain('phone')
    })
  })

  describe('Pagination and Filtering', () => {
    beforeEach(async () => {
      // Create test students for pagination tests
      await Promise.all([
        createTestStudent({ 
          studentId: '123456789001', 
          firstName: 'Alice', 
          gradeLevel: 'GRADE_11',
          section: 'STEM-A'
        }),
        createTestStudent({ 
          studentId: '123456789002', 
          firstName: 'Bob', 
          gradeLevel: 'GRADE_11',
          section: 'STEM-B'
        }),
        createTestStudent({ 
          studentId: '123456789003', 
          firstName: 'Charlie', 
          gradeLevel: 'GRADE_12',
          section: 'STEM-A'
        }),
        createTestStudent({ 
          studentId: '123456789004', 
          firstName: 'Diana', 
          gradeLevel: 'GRADE_12',
          section: 'STEM-B'
        })
      ])
    })

    it('should paginate results correctly', async () => {
      // Get first page
      const page1Request = createMockRequest('GET', 'http://localhost:3000/api/students?page=1&limit=2')
      const page1Response = await studentsGET(page1Request)
      const page1Result = await page1Response.json()

      expect(page1Response.status).toBe(200)
      expect(page1Result.students).toHaveLength(2)
      expect(page1Result.pagination.page).toBe(1)
      expect(page1Result.pagination.hasNext).toBe(true)
      expect(page1Result.pagination.hasPrev).toBe(false)

      // Get second page
      const page2Request = createMockRequest('GET', 'http://localhost:3000/api/students?page=2&limit=2')
      const page2Response = await studentsGET(page2Request)
      const page2Result = await page2Response.json()

      expect(page2Response.status).toBe(200)
      expect(page2Result.students).toHaveLength(2)
      expect(page2Result.pagination.page).toBe(2)
      expect(page2Result.pagination.hasNext).toBe(false)
      expect(page2Result.pagination.hasPrev).toBe(true)
    })

    it('should filter by grade level', async () => {
      const request = createMockRequest('GET', 'http://localhost:3000/api/students?gradeLevel=GRADE_11')
      const response = await studentsGET(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.students).toHaveLength(2)
      result.students.forEach((student: any) => {
        expect(student.gradeLevel).toBe('GRADE_11')
      })
    })

    it('should filter by section', async () => {
      const request = createMockRequest('GET', 'http://localhost:3000/api/students?section=STEM-A')
      const response = await studentsGET(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.students).toHaveLength(2)
      result.students.forEach((student: any) => {
        expect(student.section).toBe('STEM-A')
      })
    })

    it('should search by name', async () => {
      const request = createMockRequest('GET', 'http://localhost:3000/api/students?search=Alice')
      const response = await studentsGET(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.students).toHaveLength(1)
      expect(result.students[0].firstName).toBe('Alice')
    })

    it('should sort results', async () => {
      const request = createMockRequest('GET', 'http://localhost:3000/api/students?sortBy=firstName&sortOrder=asc')
      const response = await studentsGET(request)
      const result = await response.json()

      expect(response.status).toBe(200)
      expect(result.students).toHaveLength(4)
      expect(result.students[0].firstName).toBe('Alice')
      expect(result.students[1].firstName).toBe('Bob')
      expect(result.students[2].firstName).toBe('Charlie')
      expect(result.students[3].firstName).toBe('Diana')
    })
  })

  describe('Concurrent Operations', () => {
    it('should handle concurrent student creation', async () => {
      const createRequests = Array.from({ length: 5 }, (_, i) => {
        const studentData = {
          studentId: `12345678900${i}`,
          firstName: `Concurrent${i}`,
          lastName: 'Test',
          gradeLevel: 'GRADE_11',
          course: 'General Academic Strand',
          year: '2nd Year',
          guardianName: 'Test Guardian',
          guardianPhone: '09123456789',
          guardianRelationship: 'FATHER'
        }
        return createMockRequest('POST', 'http://localhost:3000/api/students', studentData)
      })

      const responses = await Promise.all(
        createRequests.map(request => studentsPOST(request))
      )

      const results = await Promise.all(
        responses.map(response => response.json())
      )

      // All should succeed
      results.forEach((result, index) => {
        expect(responses[index].status).toBe(201)
        expect(result.success).toBe(true)
        expect(result.student.firstName).toBe(`Concurrent${index}`)
      })
    })

    it('should handle concurrent updates to same student', async () => {
      // Create a student first
      const student = await createTestStudent({
        studentId: '123456789050',
        firstName: 'ConcurrentUpdate'
      })

      // Try to update the same student concurrently
      const updateRequests = [
        createMockRequest('PUT', `http://localhost:3000/api/students/${student.id}`, { section: 'STEM-A' }),
        createMockRequest('PUT', `http://localhost:3000/api/students/${student.id}`, { section: 'STEM-B' })
      ]

      const responses = await Promise.all(
        updateRequests.map(request => studentPUT(request, { params: { id: student.id } }))
      )

      // Both should succeed (last one wins)
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })
    })
  })

  describe('Error Recovery', () => {
    it('should handle database transaction rollback', async () => {
      // This test would verify that failed operations don't leave partial data
      // In a real scenario, we'd simulate a database failure during creation
      
      const invalidData = {
        studentId: '123456789060',
        firstName: 'Transaction',
        lastName: 'Test',
        gradeLevel: 'INVALID_GRADE', // This should cause validation to fail
        course: 'General Academic Strand',
        year: '2nd Year',
        guardianName: 'Test Guardian',
        guardianPhone: '09123456789',
        guardianRelationship: 'FATHER'
      }

      const request = createMockRequest('POST', 'http://localhost:3000/api/students', invalidData)
      const response = await studentsPOST(request)

      expect(response.status).toBe(400)

      // Verify no partial data was created
      const checkStudent = await prisma.student.findUnique({
        where: { studentId: '123456789060' }
      })
      expect(checkStudent).toBeNull()
    })
  })

  describe('Performance Under Load', () => {
    it('should handle multiple simultaneous read requests', async () => {
      // Create some test data
      await createTestStudent({ studentId: '123456789070', firstName: 'Performance' })

      const readRequests = Array.from({ length: 10 }, () =>
        createMockRequest('GET', 'http://localhost:3000/api/students')
      )

      const startTime = Date.now()
      const responses = await Promise.all(
        readRequests.map(request => studentsGET(request))
      )
      const endTime = Date.now()

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(5000)
    })
  })
})
