import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  exportSchema,
  type ExportData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type ExportResponse 
} from '@/lib/types/api'
import { GradeLevel, StudentStatus } from '@/lib/generated/prisma'

// GET /api/students/export - Export student data
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    
    // Parse export parameters
    const exportData: ExportData = {
      format: (searchParams.get('format') as any) || 'csv',
      filters: {
        gradeLevel: searchParams.getAll('gradeLevel') as GradeLevel[],
        section: searchParams.getAll('section'),
        status: searchParams.getAll('status') as StudentStatus[],
        course: searchParams.getAll('course'),
        year: searchParams.getAll('year')
      },
      fields: searchParams.getAll('fields'),
      options: {
        includePhotos: searchParams.get('includePhotos') === 'true',
        includeQrCodes: searchParams.get('includeQrCodes') === 'true',
        includeAttendanceStats: searchParams.get('includeAttendanceStats') === 'true',
        template: searchParams.get('template') || undefined
      }
    }

    // Validate export data
    const validation = exportSchema.safeParse(exportData)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid export parameters',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const validatedExport = validation.data

    // Build where clause for filtering
    const where: any = {}
    
    if (validatedExport.filters?.gradeLevel?.length) {
      where.gradeLevel = { in: validatedExport.filters.gradeLevel }
    }
    
    if (validatedExport.filters?.section?.length) {
      where.section = { in: validatedExport.filters.section }
    }
    
    if (validatedExport.filters?.status?.length) {
      where.status = { in: validatedExport.filters.status }
    }
    
    if (validatedExport.filters?.course?.length) {
      where.course = { in: validatedExport.filters.course }
    }
    
    if (validatedExport.filters?.year?.length) {
      where.year = { in: validatedExport.filters.year }
    }

    // Build include clause
    const include: any = {}
    if (validatedExport.options?.includeAttendanceStats) {
      include.attendanceRecords = {
        select: {
          id: true,
          date: true,
          status: true,
          timeIn: true,
          timeOut: true
        },
        orderBy: { date: 'desc' },
        take: 30
      }
    }

    // Fetch students
    const students = await prisma.student.findMany({
      where,
      include,
      orderBy: [
        { gradeLevel: 'asc' },
        { section: 'asc' },
        { lastName: 'asc' },
        { firstName: 'asc' }
      ]
    })

    // Transform students to legacy format
    const transformedStudents = students.map(student => {
      const legacyStudent = convertPrismaStudentToLegacy(student as any)
      
      // Add attendance stats if requested
      if (validatedExport.options?.includeAttendanceStats && student.attendanceRecords) {
        const records = student.attendanceRecords as any[]
        const totalDays = records.length
        const presentDays = records.filter(r => r.status === 'PRESENT').length
        const lateDays = records.filter(r => r.status === 'LATE').length
        const absentDays = records.filter(r => r.status === 'ABSENT').length
        
        legacyStudent.attendanceStats = {
          totalDays,
          presentDays,
          lateDays,
          absentDays,
          attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0
        }
      }
      
      return legacyStudent
    })

    // Define available fields for export
    const allFields = [
      'id', 'studentId', 'firstName', 'middleName', 'lastName', 'email',
      'dateOfBirth', 'gender', 'grade', 'section', 'course', 'year', 'status',
      'guardian.name', 'guardian.phone', 'guardian.email', 'guardian.relationship',
      'emergencyContacts[0].name', 'emergencyContacts[0].phone', 'emergencyContacts[0].relationship',
      'address.street', 'address.barangay', 'address.city', 'address.province', 'address.zipCode',
      'enrollmentDate', 'lastUpdated'
    ]

    const exportFields = validatedExport.fields?.length ? validatedExport.fields : allFields

    // Generate export data based on format
    let exportContent: string
    let filename: string
    let contentType: string

    switch (validatedExport.format) {
      case 'csv':
        exportContent = generateCSV(transformedStudents, exportFields, validatedExport.options)
        filename = `students_export_${new Date().toISOString().split('T')[0]}.csv`
        contentType = 'text/csv'
        break
        
      case 'excel':
        // For now, return CSV format (in a real implementation, you'd use a library like xlsx)
        exportContent = generateCSV(transformedStudents, exportFields, validatedExport.options)
        filename = `students_export_${new Date().toISOString().split('T')[0]}.xlsx`
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        break
        
      case 'pdf':
        // For now, return a simple text format (in a real implementation, you'd use a PDF library)
        exportContent = generatePDF(transformedStudents, exportFields, validatedExport.options)
        filename = `students_export_${new Date().toISOString().split('T')[0]}.pdf`
        contentType = 'application/pdf'
        break
        
      default:
        return NextResponse.json({
          success: false,
          error: 'Unsupported export format'
        } as ApiResponse, { status: 400 })
    }

    // In a real implementation, you would:
    // 1. Save the file to a temporary storage location
    // 2. Generate a secure download URL
    // 3. Set an expiration time for the download
    // 4. Return the download URL

    // For this example, we'll return the content directly
    const response = new NextResponse(exportContent, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': Buffer.byteLength(exportContent, 'utf8').toString()
      }
    })

    // Log audit trail for export
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'EXPORT',
        entityType: 'Student',
        entityId: 'bulk',
        changes: JSON.stringify({
          format: validatedExport.format,
          recordCount: transformedStudents.length,
          filters: validatedExport.filters,
          fields: exportFields
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return response

  } catch (error) {
    console.error('Export error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// Helper function to generate CSV content
function generateCSV(students: any[], fields: string[], options?: any): string {
  const headers = fields.map(field => {
    // Convert field names to readable headers
    return field
      .replace(/\[0\]/g, '')
      .replace(/\./g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .trim()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  })

  const rows = students.map(student => {
    return fields.map(field => {
      const value = getNestedValue(student, field)
      return value !== null && value !== undefined ? `"${String(value).replace(/"/g, '""')}"` : '""'
    })
  })

  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
}

// Helper function to generate PDF content (simplified)
function generatePDF(students: any[], fields: string[], options?: any): string {
  // This is a simplified text representation
  // In a real implementation, you'd use a PDF library like jsPDF or PDFKit
  let content = 'QRSAMS Student Export Report\n'
  content += `Generated: ${new Date().toLocaleString()}\n`
  content += `Total Students: ${students.length}\n\n`
  
  students.forEach((student, index) => {
    content += `${index + 1}. ${student.firstName} ${student.lastName}\n`
    content += `   Student ID: ${student.id}\n`
    content += `   Grade: ${student.grade} | Section: ${student.section || 'N/A'}\n`
    content += `   Status: ${student.status}\n\n`
  })
  
  return content
}

// Helper function to get nested object values
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    if (key.includes('[') && key.includes(']')) {
      const arrayKey = key.substring(0, key.indexOf('['))
      const index = parseInt(key.substring(key.indexOf('[') + 1, key.indexOf(']')))
      return current?.[arrayKey]?.[index]
    }
    return current?.[key]
  }, obj)
}
