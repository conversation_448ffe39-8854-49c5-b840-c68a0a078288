import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  qrGenerateSchema,
  type QRGenerateData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type QRGenerateResponse 
} from '@/lib/types/api'
import { qrGenerator, qrManager } from '@/lib/utils/qr-code'

// POST /api/students/[id]/qr-generate - Generate QR code for specific student
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.update')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: params.id }
    })

    if (!student) {
      return NextResponse.json({
        success: false,
        error: 'Student not found'
      } as ApiResponse, { status: 404 })
    }

    const body = await request.json().catch(() => ({}))
    
    // Validate QR generation options
    const qrData: QRGenerateData = {
      studentId: params.id,
      options: body.options || {}
    }

    const validation = qrGenerateSchema.safeParse(qrData)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid QR generation options',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const validatedData = validation.data
    const options = validatedData.options || {}

    // Convert to legacy format for QR generation
    const legacyStudent = convertPrismaStudentToLegacy(student as any)

    // Generate QR code data
    const qrString = qrGenerator.generateQRString(legacyStudent)
    const qrId = qrGenerator.generateQRCodeId(legacyStudent)

    // Create QR code data with encryption if requested
    let qrCodeData: string
    if (options.format === 'encrypted') {
      // In a real implementation, you would encrypt the QR data
      // For now, we'll use base64 encoding as a placeholder
      qrCodeData = Buffer.from(qrString).toString('base64')
    } else {
      qrCodeData = qrString
    }

    // Calculate expiration date
    const expiresIn = options.expiresIn || 365 // days
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + expiresIn)

    // Update student record with QR code data
    const updatedStudent = await prisma.student.update({
      where: { id: params.id },
      data: {
        qrCodeData: qrCodeData,
        updatedAt: new Date()
      }
    })

    // Generate actual QR code using the QR manager
    const qrCodeId = await qrManager.generateForStudent(legacyStudent)

    // Prepare response data
    const qrInfo = {
      qrCodeId,
      qrData: options.format === 'json' ? JSON.parse(qrString) : qrCodeData,
      format: options.format || 'encrypted',
      size: options.size || 'medium',
      expiresAt: expiresAt.toISOString(),
      generatedAt: new Date().toISOString(),
      includePhoto: options.includePhoto || false
    }

    // Log audit trail for QR generation
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'QR_GENERATE',
        entityType: 'Student',
        entityId: params.id,
        changes: JSON.stringify({
          qrCodeId,
          format: options.format,
          expiresIn,
          size: options.size
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      data: qrInfo,
      message: 'QR code generated successfully'
    } as QRGenerateResponse, { status: 200 })

  } catch (error) {
    console.error('QR generation error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// GET /api/students/[id]/qr-generate - Get existing QR code for student
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    // Check if student exists and has QR code
    const student = await prisma.student.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        studentId: true,
        firstName: true,
        lastName: true,
        qrCodeData: true,
        updatedAt: true
      }
    })

    if (!student) {
      return NextResponse.json({
        success: false,
        error: 'Student not found'
      } as ApiResponse, { status: 404 })
    }

    if (!student.qrCodeData) {
      return NextResponse.json({
        success: false,
        error: 'No QR code found for this student'
      } as ApiResponse, { status: 404 })
    }

    // Decode QR data to check validity
    let qrData: any
    try {
      // Try to decode base64 first (encrypted format)
      const decoded = Buffer.from(student.qrCodeData, 'base64').toString('utf8')
      qrData = JSON.parse(decoded)
    } catch {
      // If that fails, try parsing directly (JSON format)
      try {
        qrData = JSON.parse(student.qrCodeData)
      } catch {
        return NextResponse.json({
          success: false,
          error: 'Invalid QR code data'
        } as ApiResponse, { status: 400 })
      }
    }

    // Check if QR code is expired
    const isExpired = qrData.validUntil && new Date(qrData.validUntil) < new Date()

    const qrInfo = {
      qrCodeId: `QR_${student.studentId}_${new Date(student.updatedAt).getFullYear()}`,
      qrData: qrData,
      format: 'encrypted',
      expiresAt: qrData.validUntil,
      generatedAt: qrData.generated,
      isExpired,
      isValid: !isExpired
    }

    return NextResponse.json({
      success: true,
      data: qrInfo,
      message: isExpired ? 'QR code found but expired' : 'QR code retrieved successfully'
    } as QRGenerateResponse, { status: 200 })

  } catch (error) {
    console.error('QR retrieval error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
