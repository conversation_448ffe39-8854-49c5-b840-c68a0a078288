import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  bulkUpdateSchema,
  type BulkUpdateData 
} from '@/lib/validations/api'
import { 
  type ApiResponse 
} from '@/lib/types/api'

// POST /api/students/bulk-update - Bulk update student records
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.update')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    
    // Validate bulk update data
    const validation = bulkUpdateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid bulk update data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const { studentIds, updates, options = {} } = validation.data
    const { validateOnly = false } = options

    // Validate that students exist
    const existingStudents = await prisma.student.findMany({
      where: { id: { in: studentIds } },
      select: { id: true, studentId: true, firstName: true, lastName: true }
    })

    const existingStudentIds = new Set(existingStudents.map(s => s.id))
    const notFoundIds = studentIds.filter(id => !existingStudentIds.has(id))

    if (notFoundIds.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Students not found: ${notFoundIds.join(', ')}`
      } as ApiResponse, { status: 404 })
    }

    // If validation only, return without updating
    if (validateOnly) {
      return NextResponse.json({
        success: true,
        message: `Validation successful for ${studentIds.length} students`,
        data: {
          studentsToUpdate: existingStudents.length,
          updates: Object.keys(updates)
        }
      } as ApiResponse, { status: 200 })
    }

    // Perform bulk update in transaction
    const result = await prisma.$transaction(async (tx) => {
      const updatedStudents = await tx.student.updateMany({
        where: { id: { in: studentIds } },
        data: {
          ...updates,
          updatedAt: new Date()
        }
      })

      return updatedStudents
    })

    // Log audit trail for bulk update
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'BULK_UPDATE',
        entityType: 'Student',
        entityId: 'bulk',
        changes: JSON.stringify({
          studentIds,
          updates,
          count: result.count
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${result.count} students`,
      data: {
        updatedCount: result.count,
        updates: Object.keys(updates)
      }
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Bulk update error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
