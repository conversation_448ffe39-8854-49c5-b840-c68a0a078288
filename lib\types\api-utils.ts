// API Utility Types for Enhanced Type Safety
// This file contains utility types and helper interfaces for the student management API

import type { 
  GradeLevel, 
  StudentStatus, 
  Gender, 
  GuardianRelationship 
} from '../generated/prisma'

// Utility types for better type safety
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type WithTimestamps<T> = T & {
  createdAt: string
  updatedAt: string
}

// API endpoint parameter types
export interface RouteParams {
  id?: string
  level?: string
  section?: string
}

export interface QueryParams {
  [key: string]: string | string[] | undefined
}

// Request context types
export interface RequestContext {
  userId: string
  userRole: string
  permissions: string[]
  ipAddress?: string
  userAgent?: string
  requestId: string
  timestamp: string
}

// Validation result types
export interface ValidationResult<T = any> {
  isValid: boolean
  data?: T
  errors?: Array<{
    field: string
    message: string
    code: string
    value?: any
  }>
  warnings?: Array<{
    field: string
    message: string
    value?: any
  }>
}

// Database query options
export interface QueryOptions {
  include?: string[]
  select?: string[]
  orderBy?: Record<string, 'asc' | 'desc'>
  where?: Record<string, any>
  skip?: number
  take?: number
}

// Pagination metadata
export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
  startIndex: number
  endIndex: number
}

// Filter metadata
export interface FilterMeta {
  appliedFilters: Record<string, any>
  availableFilters: Record<string, string[]>
  filterCount: number
  totalFiltered: number
  totalUnfiltered: number
}

// Sort metadata
export interface SortMeta {
  sortBy: string
  sortOrder: 'asc' | 'desc'
  availableSortFields: string[]
}

// Search metadata
export interface SearchMeta {
  query: string
  searchFields: string[]
  fuzzySearch: boolean
  searchTime: number
  totalMatches: number
  suggestions: string[]
}

// API response metadata
export interface ResponseMeta {
  requestId: string
  timestamp: string
  executionTime: number
  version: string
  pagination?: PaginationMeta
  filters?: FilterMeta
  sort?: SortMeta
  search?: SearchMeta
}

// Enhanced API response with metadata
export interface MetadataApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  meta: ResponseMeta
}

// Student field types for better validation
export interface StudentFieldTypes {
  studentId: string // 12-digit DepEd ID
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  dateOfBirth?: Date
  gender?: Gender
  gradeLevel: GradeLevel
  section?: string
  course: string
  year: string
  status: StudentStatus
  phoneNumber?: string
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: GuardianRelationship
  address?: string
  barangay?: string
  municipality: string
  province: string
  zipCode?: string
  emergencyContactName?: string
  emergencyContactPhone?: string
  emergencyContactRelationship?: string
  photo?: string
  medicalInfo?: string
  notes?: string
}

// API operation types
export type ApiOperation = 
  | 'CREATE'
  | 'READ'
  | 'UPDATE'
  | 'DELETE'
  | 'BULK_IMPORT'
  | 'BULK_UPDATE'
  | 'BULK_DELETE'
  | 'EXPORT'
  | 'SEARCH'
  | 'QR_GENERATE'
  | 'QR_VALIDATE'

// Permission requirements for operations
export interface OperationPermissions {
  operation: ApiOperation
  requiredPermissions: string[]
  requiredRole?: string
  additionalChecks?: string[]
}

// Audit trail types
export interface AuditTrailEntry {
  operation: ApiOperation
  entityType: 'Student'
  entityId: string
  userId: string
  timestamp: string
  changes?: {
    before?: Record<string, any>
    after?: Record<string, any>
  }
  metadata?: {
    ipAddress?: string
    userAgent?: string
    requestId?: string
    bulkOperation?: boolean
    affectedCount?: number
  }
}

// Rate limiting types
export interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  keyGenerator?: (req: any) => string
}

export interface RateLimitInfo {
  limit: number
  remaining: number
  resetTime: Date
  retryAfter?: number
}

// Cache types
export interface CacheConfig {
  ttl: number // Time to live in seconds
  key: string
  tags?: string[]
  invalidateOn?: ApiOperation[]
}

export interface CacheInfo {
  hit: boolean
  key: string
  ttl: number
  createdAt: Date
  expiresAt: Date
}

// File upload types
export interface FileUploadConfig {
  maxSize: number // in bytes
  allowedTypes: string[]
  uploadPath: string
  generateThumbnail?: boolean
  compressionQuality?: number
}

export interface UploadedFile {
  filename: string
  originalName: string
  mimeType: string
  size: number
  path: string
  url: string
  thumbnailUrl?: string
}

// Export configuration types
export interface ExportFieldConfig {
  field: string
  label: string
  type: 'string' | 'number' | 'date' | 'boolean' | 'enum'
  format?: string
  transform?: (value: any) => any
}

export interface ExportTemplate {
  name: string
  description: string
  fields: ExportFieldConfig[]
  defaultFilters?: Record<string, any>
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// Import configuration types
export interface ImportFieldMapping {
  csvColumn: string
  dbField: string
  required: boolean
  validator?: (value: any) => ValidationResult
  transformer?: (value: any) => any
}

export interface ImportTemplate {
  name: string
  description: string
  fieldMappings: ImportFieldMapping[]
  requiredColumns: string[]
  optionalColumns: string[]
  sampleData?: Record<string, any>[]
}

// QR Code configuration types
export interface QRCodeConfig {
  size: 'small' | 'medium' | 'large'
  format: 'json' | 'encrypted'
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H'
  margin: number
  includePhoto: boolean
  expirationDays: number
  encryptionKey?: string
}

export interface QRCodeBatchConfig extends QRCodeConfig {
  batchSize: number
  outputFormat: 'individual' | 'sheet' | 'pdf'
  sheetLayout?: {
    columns: number
    rows: number
    spacing: number
  }
}

// Notification types
export interface NotificationConfig {
  type: 'email' | 'sms' | 'push'
  template: string
  recipients: string[]
  priority: 'low' | 'normal' | 'high' | 'urgent'
  scheduledAt?: Date
  metadata?: Record<string, any>
}

// Health check types
export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  checks: {
    database: boolean
    cache: boolean
    storage: boolean
    externalServices: boolean
  }
  metrics: {
    responseTime: number
    memoryUsage: number
    cpuUsage: number
    activeConnections: number
  }
}

// API versioning types
export interface ApiVersion {
  version: string
  deprecated: boolean
  supportedUntil?: Date
  migrationGuide?: string
}

// Feature flag types
export interface FeatureFlag {
  name: string
  enabled: boolean
  description: string
  rolloutPercentage?: number
  conditions?: Record<string, any>
}

// Type guards for runtime type checking
export const isValidGradeLevel = (value: any): value is GradeLevel => {
  return typeof value === 'string' && 
    ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12'].includes(value)
}

export const isValidStudentStatus = (value: any): value is StudentStatus => {
  return typeof value === 'string' && 
    ['ACTIVE', 'INACTIVE', 'TRANSFERRED', 'GRADUATED', 'DROPPED'].includes(value)
}

export const isValidGender = (value: any): value is Gender => {
  return typeof value === 'string' && ['MALE', 'FEMALE'].includes(value)
}

// Utility functions for type conversion
export const toGradeLevel = (grade: string | number): GradeLevel => {
  const gradeNum = typeof grade === 'string' ? parseInt(grade) : grade
  return `GRADE_${gradeNum}` as GradeLevel
}

export const fromGradeLevel = (gradeLevel: GradeLevel): number => {
  return parseInt(gradeLevel.replace('GRADE_', ''))
}

// Type-safe object utilities
export const pickFields = <T, K extends keyof T>(obj: T, fields: K[]): Pick<T, K> => {
  const result = {} as Pick<T, K>
  fields.forEach(field => {
    if (field in obj) {
      result[field] = obj[field]
    }
  })
  return result
}

export const omitFields = <T, K extends keyof T>(obj: T, fields: K[]): Omit<T, K> => {
  const result = { ...obj } as any
  fields.forEach(field => {
    delete result[field]
  })
  return result
}
