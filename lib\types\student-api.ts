// Comprehensive Student Management API Types
// This file consolidates all types specifically for student management API endpoints

import type {
  GradeLevel,
  StudentStatus,
  Gender,
  GuardianRelationship
} from '../generated/prisma'

import type {
  ApiResponse,
  PaginatedResponse,
  ValidationError,
  AttendanceStats,
  RiskAssessment,
  EnhancedStudentData
} from './api'

import type {
  RequestContext,
  ValidationResult,
  QueryOptions,
  ResponseMeta,
  AuditTrailEntry
} from './api-utils'

// ============================================================================
// CORE STUDENT TYPES
// ============================================================================

export interface StudentCore {
  id: string
  studentId: string // 12-digit DepEd ID
  firstName: string
  lastName: string
  middleName?: string
  fullName: string
}

export interface StudentPersonal extends StudentCore {
  email?: string
  dateOfBirth?: string
  age?: number
  gender?: Gender
  photo?: string
}

export interface StudentAcademic extends StudentPersonal {
  gradeLevel: GradeLevel
  section?: string
  course: string
  year: string
  status: StudentStatus
  enrollmentDate: string
}

export interface StudentContact extends StudentAcademic {
  phoneNumber?: string
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: GuardianRelationship
  emergencyContactName?: string
  emergencyContactPhone?: string
  emergencyContactRelationship?: string
}

export interface StudentAddress extends StudentContact {
  address?: string
  barangay?: string
  municipality: string
  province: string
  zipCode?: string
}

export interface StudentComplete extends StudentAddress {
  medicalInfo?: string
  notes?: string
  qrCode?: string
  attendanceStats?: AttendanceStats
  riskAssessment?: RiskAssessment
  createdAt: string
  updatedAt: string
}

// ============================================================================
// REQUEST TYPES
// ============================================================================

export interface CreateStudentRequest {
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  dateOfBirth?: string
  gender?: Gender
  gradeLevel: GradeLevel
  section?: string
  course: string
  year: string
  phoneNumber?: string
  guardianName: string
  guardianPhone: string
  guardianEmail?: string
  guardianRelationship: GuardianRelationship
  address?: string
  barangay?: string
  municipality?: string
  province?: string
  zipCode?: string
  emergencyContactName?: string
  emergencyContactPhone?: string
  emergencyContactRelationship?: string
  photo?: string
  medicalInfo?: string
  notes?: string
}

export interface UpdateStudentRequest extends Partial<CreateStudentRequest> {
  status?: StudentStatus
}

export interface StudentQueryRequest {
  // Pagination
  page?: number
  limit?: number
  
  // Filters
  search?: string
  gradeLevel?: GradeLevel[]
  section?: string[]
  status?: StudentStatus[]
  course?: string[]
  year?: string[]
  gender?: Gender[]
  municipality?: string[]
  hasPhoto?: boolean
  hasQrCode?: boolean
  enrollmentDateFrom?: string
  enrollmentDateTo?: string
  
  // Sorting
  sortBy?: 'firstName' | 'lastName' | 'studentId' | 'gradeLevel' | 'section' | 'enrollmentDate' | 'lastUpdated'
  sortOrder?: 'asc' | 'desc'
  
  // Include options
  include?: ('attendanceStats' | 'recentAttendance' | 'riskAssessment' | 'qrCode')[]
}

// ============================================================================
// RESPONSE TYPES
// ============================================================================

export interface StudentResponse extends ApiResponse<StudentComplete> {
  student: StudentComplete
}

export interface StudentsListResponse extends PaginatedResponse<StudentComplete> {
  students: StudentComplete[]
  summary?: {
    totalActive: number
    totalInactive: number
    totalByGrade: Record<string, number>
    totalBySection: Record<string, number>
    totalByStatus: Record<string, number>
  }
}

export interface GradeStudentsResponse extends PaginatedResponse<StudentComplete> {
  students: StudentComplete[]
  gradeLevel: GradeLevel
  gradeStats: {
    totalStudents: number
    sections: string[]
    averageAge?: number
    statusDistribution: Record<StudentStatus, number>
  }
}

export interface SectionStudentsResponse extends PaginatedResponse<StudentComplete> {
  students: StudentComplete[]
  section: string
  sectionStats: {
    totalStudents: number
    gradeLevel: GradeLevel
    averageAge?: number
    genderDistribution: Record<Gender, number>
  }
}

// ============================================================================
// BULK OPERATION TYPES
// ============================================================================

export interface BulkImportRequest {
  students: CreateStudentRequest[]
  options?: {
    skipDuplicates?: boolean
    updateExisting?: boolean
    validateOnly?: boolean
    batchSize?: number
  }
}

export interface BulkImportResponse extends ApiResponse {
  results: {
    total: number
    imported: number
    updated: number
    skipped: number
    failed: number
  }
  errors?: Array<{
    row: number
    studentId?: string
    field?: string
    message: string
    data?: any
  }>
  warnings?: Array<{
    row: number
    studentId?: string
    message: string
  }>
  processingTime: number
  batchId: string
}

export interface BulkUpdateRequest {
  studentIds: string[]
  updates: Partial<UpdateStudentRequest>
  options?: {
    skipValidation?: boolean
    batchSize?: number
  }
}

export interface BulkUpdateResponse extends ApiResponse {
  results: {
    total: number
    updated: number
    failed: number
  }
  errors?: Array<{
    studentId: string
    message: string
  }>
  updatedFields: string[]
  processingTime: number
}

export interface BulkDeleteRequest {
  studentIds: string[]
  options?: {
    hardDelete?: boolean
    reason?: string
    batchSize?: number
  }
}

export interface BulkDeleteResponse extends ApiResponse {
  results: {
    total: number
    deleted: number
    failed: number
  }
  errors?: Array<{
    studentId: string
    message: string
  }>
  deletionType: 'soft' | 'hard'
  processingTime: number
}

// ============================================================================
// SEARCH TYPES
// ============================================================================

export interface SearchRequest {
  query: string
  filters?: Partial<StudentQueryRequest>
  options?: {
    fuzzy?: boolean
    fields?: string[]
    limit?: number
    highlightMatches?: boolean
  }
}

export interface SearchMatch {
  field: string
  value: string
  highlighted?: string
  relevance: number
}

export interface SearchResult {
  student: StudentComplete
  score: number
  matches: SearchMatch[]
  relevanceFactors: string[]
}

export interface SearchResponse extends ApiResponse {
  results: SearchResult[]
  totalResults: number
  searchTime: number
  searchQuery: string
  suggestions?: string[]
  facets?: {
    grades: Record<string, number>
    sections: Record<string, number>
    statuses: Record<string, number>
    courses: Record<string, number>
  }
}

// ============================================================================
// QR CODE TYPES
// ============================================================================

export interface QRGenerateRequest {
  studentId?: string
  studentIds?: string[]
  options?: {
    format?: 'json' | 'encrypted'
    expiresIn?: number // days
    includePhoto?: boolean
    size?: 'small' | 'medium' | 'large'
    batchName?: string
  }
}

export interface QRCodeData {
  id: string
  studentId: string
  name: string
  grade: string
  section?: string
  school: string
  type: 'student_id'
  generated: string
  validUntil?: string
  version: string
}

export interface QRGenerateResponse extends ApiResponse {
  qrCode?: string
  qrCodes?: Array<{
    studentId: string
    studentName: string
    qrCode: string
    qrData: string
    expiresAt?: string
    downloadUrl?: string
  }>
  batchId?: string
  downloadUrl?: string
  generationTime: number
  totalGenerated: number
}

export interface QRValidateRequest {
  qrCode: string
  qrData?: string
}

export interface QRValidateResponse extends ApiResponse {
  valid: boolean
  student?: StudentComplete
  qrData?: QRCodeData
  validationDetails: {
    structureValid: boolean
    integrityValid: boolean
    notExpired: boolean
    studentExists: boolean
    dataMatches: boolean
  }
  validationErrors?: string[]
  validationTime: number
}

// ============================================================================
// EXPORT TYPES
// ============================================================================

export interface ExportRequest {
  format: 'csv' | 'excel' | 'pdf'
  filters?: Partial<StudentQueryRequest>
  fields?: string[]
  options?: {
    includePhotos?: boolean
    includeQrCodes?: boolean
    includeAttendanceStats?: boolean
    template?: string
    filename?: string
  }
}

export interface ExportResponse extends ApiResponse {
  downloadUrl: string
  filename: string
  fileSize: number
  recordCount: number
  format: 'csv' | 'excel' | 'pdf'
  expiresAt: string
  generationTime: number
  includedFields: string[]
  filters?: Record<string, any>
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type StudentField = keyof StudentComplete
export type RequiredStudentFields = keyof CreateStudentRequest
export type OptionalStudentFields = keyof UpdateStudentRequest

export interface StudentValidationRules {
  field: StudentField
  required: boolean
  type: 'string' | 'number' | 'date' | 'email' | 'phone' | 'enum'
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  enumValues?: string[]
  customValidator?: (value: any) => ValidationResult
}

export interface StudentFieldMetadata {
  field: StudentField
  label: string
  description: string
  type: 'string' | 'number' | 'date' | 'email' | 'phone' | 'enum' | 'boolean'
  required: boolean
  searchable: boolean
  sortable: boolean
  filterable: boolean
  exportable: boolean
  sensitive: boolean
}

// ============================================================================
// API ENDPOINT TYPES
// ============================================================================

export interface StudentApiEndpoints {
  // Core CRUD
  'GET /api/students': {
    query: StudentQueryRequest
    response: StudentsListResponse
  }
  'POST /api/students': {
    body: CreateStudentRequest
    response: StudentResponse
  }
  'GET /api/students/[id]': {
    params: { id: string }
    query?: { include?: string[] }
    response: StudentResponse
  }
  'PUT /api/students/[id]': {
    params: { id: string }
    body: UpdateStudentRequest
    response: StudentResponse
  }
  'DELETE /api/students/[id]': {
    params: { id: string }
    query?: { hardDelete?: boolean }
    response: ApiResponse
  }
  
  // Advanced queries
  'GET /api/students/grade/[level]': {
    params: { level: string }
    query: Omit<StudentQueryRequest, 'gradeLevel'>
    response: GradeStudentsResponse
  }
  'GET /api/students/section/[section]': {
    params: { section: string }
    query: Omit<StudentQueryRequest, 'section'>
    response: SectionStudentsResponse
  }
  'GET /api/students/search': {
    query: SearchRequest
    response: SearchResponse
  }
  
  // Bulk operations
  'POST /api/students/bulk-import': {
    body: BulkImportRequest
    response: BulkImportResponse
  }
  'POST /api/students/bulk-update': {
    body: BulkUpdateRequest
    response: BulkUpdateResponse
  }
  'DELETE /api/students/bulk-delete': {
    body: BulkDeleteRequest
    response: BulkDeleteResponse
  }
  'GET /api/students/export': {
    query: ExportRequest
    response: ExportResponse
  }
  
  // QR Code operations
  'POST /api/students/[id]/qr-generate': {
    params: { id: string }
    body?: Omit<QRGenerateRequest, 'studentId' | 'studentIds'>
    response: QRGenerateResponse
  }
  'POST /api/students/qr-bulk-generate': {
    body: QRGenerateRequest
    response: QRGenerateResponse
  }
  'POST /api/students/qr-validate': {
    body: QRValidateRequest
    response: QRValidateResponse
  }
}

// Type helper for extracting endpoint types
export type EndpointRequest<T extends keyof StudentApiEndpoints> = StudentApiEndpoints[T] extends {
  body: infer B
} ? B : StudentApiEndpoints[T] extends {
  query: infer Q
} ? Q : never

export type EndpointResponse<T extends keyof StudentApiEndpoints> = StudentApiEndpoints[T]['response']

export type EndpointParams<T extends keyof StudentApiEndpoints> = StudentApiEndpoints[T] extends {
  params: infer P
} ? P : never
