import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  bulkDeleteSchema,
  type BulkDeleteData 
} from '@/lib/validations/api'
import { 
  type ApiResponse 
} from '@/lib/types/api'

// DELETE /api/students/bulk-delete - Bulk soft delete student records
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.delete')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    
    // Validate bulk delete data
    const validation = bulkDeleteSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid bulk delete data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const { studentIds, options = {} } = validation.data
    const { hardDelete = false, validateOnly = false } = options

    // Validate that students exist
    const existingStudents = await prisma.student.findMany({
      where: { id: { in: studentIds } },
      select: { 
        id: true, 
        studentId: true, 
        firstName: true, 
        lastName: true, 
        status: true,
        attendanceRecords: { select: { id: true } }
      }
    })

    const existingStudentIds = new Set(existingStudents.map(s => s.id))
    const notFoundIds = studentIds.filter(id => !existingStudentIds.has(id))

    if (notFoundIds.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Students not found: ${notFoundIds.join(', ')}`
      } as ApiResponse, { status: 404 })
    }

    // Check for students with attendance records if hard delete is requested
    const studentsWithAttendance = existingStudents.filter(s => s.attendanceRecords.length > 0)
    
    if (hardDelete && studentsWithAttendance.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Cannot hard delete students with attendance records: ${studentsWithAttendance.map(s => s.studentId).join(', ')}`
      } as ApiResponse, { status: 400 })
    }

    // If validation only, return without deleting
    if (validateOnly) {
      return NextResponse.json({
        success: true,
        message: `Validation successful for ${studentIds.length} students`,
        data: {
          studentsToDelete: existingStudents.length,
          deleteType: hardDelete ? 'hard' : 'soft',
          studentsWithAttendance: studentsWithAttendance.length
        }
      } as ApiResponse, { status: 200 })
    }

    // Perform bulk delete in transaction
    const result = await prisma.$transaction(async (tx) => {
      if (hardDelete) {
        // Hard delete - actually remove from database
        // Only for students without attendance records
        const studentsToHardDelete = existingStudents
          .filter(s => s.attendanceRecords.length === 0)
          .map(s => s.id)
        
        if (studentsToHardDelete.length > 0) {
          const deletedStudents = await tx.student.deleteMany({
            where: { id: { in: studentsToHardDelete } }
          })
          return { count: deletedStudents.count, type: 'hard' }
        }
        return { count: 0, type: 'hard' }
      } else {
        // Soft delete - update status to DROPPED
        const updatedStudents = await tx.student.updateMany({
          where: { id: { in: studentIds } },
          data: {
            status: 'DROPPED',
            updatedAt: new Date()
          }
        })
        return { count: updatedStudents.count, type: 'soft' }
      }
    })

    // Log audit trail for bulk delete
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: hardDelete ? 'BULK_HARD_DELETE' : 'BULK_SOFT_DELETE',
        entityType: 'Student',
        entityId: 'bulk',
        changes: JSON.stringify({
          studentIds,
          count: result.count,
          deleteType: result.type,
          studentsWithAttendance: studentsWithAttendance.length
        }),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      message: `Successfully ${result.type} deleted ${result.count} students`,
      data: {
        deletedCount: result.count,
        deleteType: result.type
      }
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Bulk delete error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/students/bulk-delete - Alternative POST method for bulk delete
export async function POST(request: NextRequest) {
  // Some clients prefer POST for bulk operations
  return DELETE(request)
}
