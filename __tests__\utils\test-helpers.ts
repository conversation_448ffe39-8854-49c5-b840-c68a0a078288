// Test Utilities and Helpers for Student Management API Tests
// Provides common test utilities, mock data generators, and helper functions

import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import type { 
  CreateStudentRequest,
  UpdateStudentRequest,
  StudentComplete 
} from '@/lib/types/student-api'
import { GradeLevel, StudentStatus, Gender, GuardianRelationship } from '@/lib/generated/prisma'

// ============================================================================
// MOCK DATA GENERATORS
// ============================================================================

export const generateMockStudentData = (overrides: Partial<CreateStudentRequest> = {}): CreateStudentRequest => {
  const randomId = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
  
  return {
    studentId: `123456${randomId}`,
    firstName: 'Test',
    lastName: 'Student',
    middleName: 'Mock',
    email: `test.student${randomId}@student.tanauan.edu.ph`,
    dateOfBirth: '2007-05-15',
    gender: 'MALE' as Gender,
    gradeLevel: 'GRADE_11' as GradeLevel,
    section: 'STEM-A',
    course: 'Science, Technology, Engineering and Mathematics',
    year: '2nd Year',
    phoneNumber: '09123456789',
    guardianName: 'Test Guardian',
    guardianPhone: '09123456789',
    guardianEmail: `guardian${randomId}@email.com`,
    guardianRelationship: 'FATHER' as GuardianRelationship,
    address: '123 Test Street',
    barangay: 'Test Barangay',
    municipality: 'Tanauan',
    province: 'Leyte',
    zipCode: '6502',
    emergencyContactName: 'Emergency Contact',
    emergencyContactPhone: '09123456789',
    emergencyContactRelationship: 'UNCLE',
    photo: 'https://example.com/photo.jpg',
    medicalInfo: 'No known allergies',
    notes: 'Test student for automated testing',
    ...overrides
  }
}

export const generateMockStudentList = (count: number, baseOverrides: Partial<CreateStudentRequest> = {}): CreateStudentRequest[] => {
  return Array.from({ length: count }, (_, index) => 
    generateMockStudentData({
      ...baseOverrides,
      firstName: `Student${index + 1}`,
      studentId: `12345678${(1000 + index).toString()}`,
      email: `student${index + 1}@student.tanauan.edu.ph`
    })
  )
}

export const generateGradeDistribution = (): CreateStudentRequest[] => {
  const grades: GradeLevel[] = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12']
  const sections = ['A', 'B', 'C']
  const students: CreateStudentRequest[] = []

  grades.forEach((grade, gradeIndex) => {
    sections.forEach((section, sectionIndex) => {
      const studentCount = Math.floor(Math.random() * 5) + 3 // 3-7 students per section
      for (let i = 0; i < studentCount; i++) {
        const studentNumber = gradeIndex * 100 + sectionIndex * 10 + i
        students.push(generateMockStudentData({
          gradeLevel: grade,
          section: `${grade.replace('GRADE_', '')}-${section}`,
          studentId: `123456${studentNumber.toString().padStart(6, '0')}`,
          firstName: `Student${studentNumber}`,
          email: `student${studentNumber}@student.tanauan.edu.ph`
        }))
      }
    })
  })

  return students
}

// ============================================================================
// REQUEST HELPERS
// ============================================================================

export const createMockRequest = (
  method: string,
  url: string,
  body?: any,
  headers?: Record<string, string>
): NextRequest => {
  const requestInit: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'test-agent',
      'X-Forwarded-For': '127.0.0.1',
      ...headers
    }
  }

  if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    requestInit.body = JSON.stringify(body)
  }

  return new NextRequest(url, requestInit)
}

export const createAuthenticatedRequest = (
  method: string,
  url: string,
  body?: any,
  userRole: string = 'ADMIN',
  permissions: string[] = ['students:read', 'students:write', 'students:delete']
): NextRequest => {
  return createMockRequest(method, url, body, {
    'Authorization': 'Bearer test-token',
    'X-User-Role': userRole,
    'X-User-Permissions': permissions.join(',')
  })
}

// ============================================================================
// RESPONSE HELPERS
// ============================================================================

export const expectSuccessResponse = (response: Response, expectedStatus: number = 200) => {
  expect(response.status).toBe(expectedStatus)
  return response.json()
}

export const expectErrorResponse = (response: Response, expectedStatus: number = 400) => {
  expect(response.status).toBe(expectedStatus)
  return response.json()
}

export const expectValidationError = async (response: Response, field?: string) => {
  expect(response.status).toBe(400)
  const data = await response.json()
  expect(data.success).toBe(false)
  expect(data.validationErrors || data.error).toBeDefined()
  
  if (field) {
    const errorMessage = data.validationErrors?.[0]?.message || data.error
    expect(errorMessage.toLowerCase()).toContain(field.toLowerCase())
  }
  
  return data
}

export const expectPaginatedResponse = async (response: Response, expectedCount?: number) => {
  expect(response.status).toBe(200)
  const data = await response.json()
  expect(data.success).toBe(true)
  expect(data.students).toBeInstanceOf(Array)
  expect(data.pagination).toBeDefined()
  expect(data.pagination).toHaveProperty('page')
  expect(data.pagination).toHaveProperty('limit')
  expect(data.pagination).toHaveProperty('total')
  expect(data.pagination).toHaveProperty('totalPages')
  expect(data.pagination).toHaveProperty('hasNext')
  expect(data.pagination).toHaveProperty('hasPrev')
  
  if (expectedCount !== undefined) {
    expect(data.students).toHaveLength(expectedCount)
  }
  
  return data
}

// ============================================================================
// DATABASE HELPERS
// ============================================================================

export const createTestStudent = async (overrides: Partial<CreateStudentRequest> = {}) => {
  const studentData = generateMockStudentData(overrides)
  
  return await prisma.student.create({
    data: {
      ...studentData,
      enrollmentDate: new Date(),
      status: 'ACTIVE' as StudentStatus
    }
  })
}

export const createTestStudents = async (count: number, baseOverrides: Partial<CreateStudentRequest> = {}) => {
  const studentsData = generateMockStudentList(count, baseOverrides)
  
  return await Promise.all(
    studentsData.map(data => 
      prisma.student.create({
        data: {
          ...data,
          enrollmentDate: new Date(),
          status: 'ACTIVE' as StudentStatus
        }
      })
    )
  )
}

export const cleanupTestStudents = async (prefix: string = 'Test') => {
  await prisma.student.deleteMany({
    where: {
      OR: [
        { firstName: { startsWith: prefix } },
        { lastName: { startsWith: prefix } },
        { studentId: { startsWith: '123456' } } // Test student IDs
      ]
    }
  })
}

export const findTestStudent = async (studentId: string) => {
  return await prisma.student.findUnique({
    where: { studentId }
  })
}

export const countTestStudents = async (filter: any = {}) => {
  return await prisma.student.count({
    where: {
      firstName: { startsWith: 'Test' },
      ...filter
    }
  })
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

export const validateStudentResponse = (student: any) => {
  expect(student).toHaveProperty('id')
  expect(student).toHaveProperty('studentId')
  expect(student).toHaveProperty('firstName')
  expect(student).toHaveProperty('lastName')
  expect(student).toHaveProperty('gradeLevel')
  expect(student).toHaveProperty('status')
  expect(student).toHaveProperty('createdAt')
  expect(student).toHaveProperty('updatedAt')
  
  // Validate required fields are not null/undefined
  expect(student.studentId).toBeTruthy()
  expect(student.firstName).toBeTruthy()
  expect(student.lastName).toBeTruthy()
  expect(student.gradeLevel).toBeTruthy()
  expect(student.status).toBeTruthy()
}

export const validatePaginationMeta = (pagination: any, expectedTotal?: number) => {
  expect(pagination).toHaveProperty('page')
  expect(pagination).toHaveProperty('limit')
  expect(pagination).toHaveProperty('total')
  expect(pagination).toHaveProperty('totalPages')
  expect(pagination).toHaveProperty('hasNext')
  expect(pagination).toHaveProperty('hasPrev')
  
  expect(typeof pagination.page).toBe('number')
  expect(typeof pagination.limit).toBe('number')
  expect(typeof pagination.total).toBe('number')
  expect(typeof pagination.totalPages).toBe('number')
  expect(typeof pagination.hasNext).toBe('boolean')
  expect(typeof pagination.hasPrev).toBe('boolean')
  
  expect(pagination.page).toBeGreaterThan(0)
  expect(pagination.limit).toBeGreaterThan(0)
  expect(pagination.total).toBeGreaterThanOrEqual(0)
  expect(pagination.totalPages).toBeGreaterThanOrEqual(0)
  
  if (expectedTotal !== undefined) {
    expect(pagination.total).toBe(expectedTotal)
  }
}

export const validateDepEdId = (studentId: string) => {
  expect(studentId).toMatch(/^\d{12}$/)
}

export const validatePhilippinePhone = (phone: string) => {
  expect(phone).toMatch(/^(\+63|0)?[0-9\s\-\(\)]+$/)
}

export const validateGradeLevel = (gradeLevel: string) => {
  const validGrades = ['GRADE_7', 'GRADE_8', 'GRADE_9', 'GRADE_10', 'GRADE_11', 'GRADE_12']
  expect(validGrades).toContain(gradeLevel)
}

// ============================================================================
// PERFORMANCE HELPERS
// ============================================================================

export const measureExecutionTime = async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
  const startTime = Date.now()
  const result = await fn()
  const endTime = Date.now()
  return { result, duration: endTime - startTime }
}

export const expectPerformance = (duration: number, maxDuration: number, operation: string) => {
  expect(duration).toBeLessThan(maxDuration)
  console.log(`${operation} completed in ${duration}ms (max: ${maxDuration}ms)`)
}

// ============================================================================
// BULK OPERATION HELPERS
// ============================================================================

export const generateBulkImportData = (count: number, options: {
  withErrors?: boolean
  duplicateIds?: boolean
  invalidData?: boolean
} = {}) => {
  const students = generateMockStudentList(count)
  
  if (options.withErrors) {
    // Introduce some validation errors
    students[0].studentId = '12345' // Too short
    students[1].guardianPhone = 'invalid-phone'
    students[2].gradeLevel = 'INVALID_GRADE' as any
  }
  
  if (options.duplicateIds) {
    // Create duplicate student IDs
    students[1].studentId = students[0].studentId
  }
  
  if (options.invalidData) {
    // Remove required fields
    delete (students[0] as any).firstName
    delete (students[1] as any).guardianName
  }
  
  return students
}

export const validateBulkOperationResponse = (response: any, expectedTotal: number) => {
  expect(response).toHaveProperty('results')
  expect(response.results).toHaveProperty('total')
  expect(response.results).toHaveProperty('imported')
  expect(response.results).toHaveProperty('failed')
  expect(response.results.total).toBe(expectedTotal)
  expect(response.results.imported + response.results.failed).toBe(expectedTotal)
}

// ============================================================================
// QR CODE HELPERS
// ============================================================================

export const generateMockQRData = (studentId: string, overrides: any = {}) => {
  return {
    id: `qr-${studentId}`,
    studentId,
    name: 'Test Student',
    grade: 'GRADE_11',
    section: 'STEM-A',
    school: 'Tanauan National High School',
    type: 'student_id',
    generated: new Date().toISOString(),
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
    version: '1.0',
    ...overrides
  }
}

export const validateQRResponse = (response: any) => {
  expect(response).toHaveProperty('qrCode')
  expect(response).toHaveProperty('qrData')
  expect(response).toHaveProperty('generationTime')
  expect(typeof response.generationTime).toBe('number')
}

export const validateQRValidationResponse = (response: any) => {
  expect(response).toHaveProperty('valid')
  expect(response).toHaveProperty('validationDetails')
  expect(response).toHaveProperty('validationTime')
  expect(typeof response.valid).toBe('boolean')
  expect(typeof response.validationTime).toBe('number')
  
  const details = response.validationDetails
  expect(details).toHaveProperty('structureValid')
  expect(details).toHaveProperty('integrityValid')
  expect(details).toHaveProperty('notExpired')
  expect(details).toHaveProperty('studentExists')
  expect(details).toHaveProperty('dataMatches')
}

// ============================================================================
// ERROR SIMULATION HELPERS
// ============================================================================

export const simulateDatabaseError = (mockPrisma: any, operation: string) => {
  mockPrisma[operation].mockRejectedValue(new Error('Database connection failed'))
}

export const simulateNetworkError = () => {
  throw new Error('Network request failed')
}

export const simulateTimeout = (delay: number = 5000) => {
  return new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), delay)
  })
}

// ============================================================================
// EXPORT HELPERS
// ============================================================================

export const validateExportResponse = (response: any) => {
  expect(response).toHaveProperty('downloadUrl')
  expect(response).toHaveProperty('filename')
  expect(response).toHaveProperty('fileSize')
  expect(response).toHaveProperty('recordCount')
  expect(response).toHaveProperty('format')
  expect(response).toHaveProperty('expiresAt')
  expect(response).toHaveProperty('generationTime')
  
  expect(typeof response.fileSize).toBe('number')
  expect(typeof response.recordCount).toBe('number')
  expect(typeof response.generationTime).toBe('number')
  expect(['csv', 'excel', 'pdf']).toContain(response.format)
}
