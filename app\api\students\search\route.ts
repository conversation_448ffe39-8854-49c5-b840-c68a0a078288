import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/config'
import { hasPermission } from '@/lib/auth/permissions'
import { prisma } from '@/lib/prisma'
import { 
  searchSchema,
  type SearchData 
} from '@/lib/validations/api'
import { 
  convertPrismaStudentToLegacy
} from '@/lib/types/prisma'
import { 
  type ApiResponse, 
  type SearchResponse 
} from '@/lib/types/api'
import { GradeLevel, StudentStatus } from '@/lib/generated/prisma'

// GET /api/students/search - Advanced search functionality
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    
    // Parse and validate search parameters
    const searchData: SearchData = {
      query: searchParams.get('query') || '',
      filters: {
        gradeLevel: searchParams.getAll('gradeLevel') as GradeLevel[],
        section: searchParams.getAll('section'),
        status: searchParams.getAll('status') as StudentStatus[],
        course: searchParams.getAll('course'),
        year: searchParams.getAll('year')
      },
      options: {
        fuzzy: searchParams.get('fuzzy') === 'true',
        fields: searchParams.getAll('fields'),
        limit: Math.min(parseInt(searchParams.get('limit') || '50'), 100),
        highlightMatches: searchParams.get('highlightMatches') === 'true'
      }
    }

    // Validate search data
    const validation = searchSchema.safeParse(searchData)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid search parameters',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    const validatedSearch = validation.data

    if (!validatedSearch.query.trim()) {
      return NextResponse.json({
        success: false,
        error: 'Search query is required'
      } as ApiResponse, { status: 400 })
    }

    const startTime = Date.now()

    // Build base where clause with filters
    const baseWhere: any = {}
    
    if (validatedSearch.filters?.gradeLevel?.length) {
      baseWhere.gradeLevel = { in: validatedSearch.filters.gradeLevel }
    }
    
    if (validatedSearch.filters?.section?.length) {
      baseWhere.section = { in: validatedSearch.filters.section }
    }
    
    if (validatedSearch.filters?.status?.length) {
      baseWhere.status = { in: validatedSearch.filters.status }
    }
    
    if (validatedSearch.filters?.course?.length) {
      baseWhere.course = { in: validatedSearch.filters.course }
    }
    
    if (validatedSearch.filters?.year?.length) {
      baseWhere.year = { in: validatedSearch.filters.year }
    }

    // Define searchable fields
    const defaultFields = ['firstName', 'lastName', 'middleName', 'studentId', 'email', 'course', 'section']
    const searchFields = validatedSearch.options?.fields?.length 
      ? validatedSearch.options.fields 
      : defaultFields

    // Build search conditions
    const searchTerms = validatedSearch.query.trim().split(/\s+/)
    const searchConditions: any[] = []

    // Exact match conditions
    searchFields.forEach(field => {
      searchConditions.push({
        [field]: { contains: validatedSearch.query, mode: 'insensitive' }
      })
    })

    // Individual term matching for better results
    if (searchTerms.length > 1) {
      searchTerms.forEach(term => {
        if (term.length >= 2) {
          searchFields.forEach(field => {
            searchConditions.push({
              [field]: { contains: term, mode: 'insensitive' }
            })
          })
        }
      })
    }

    // Fuzzy matching for student ID (partial matches)
    if (validatedSearch.options?.fuzzy && /\d/.test(validatedSearch.query)) {
      const numericQuery = validatedSearch.query.replace(/\D/g, '')
      if (numericQuery.length >= 3) {
        searchConditions.push({
          studentId: { contains: numericQuery }
        })
      }
    }

    const where = {
      ...baseWhere,
      OR: searchConditions
    }

    // Execute search
    const students = await prisma.student.findMany({
      where,
      take: validatedSearch.options?.limit || 50,
      orderBy: [
        { status: 'asc' }, // Active students first
        { lastName: 'asc' },
        { firstName: 'asc' }
      ]
    })

    // Calculate relevance scores and highlight matches
    const results = students.map(student => {
      const legacyStudent = convertPrismaStudentToLegacy(student as any)
      let score = 0
      const matches: Array<{ field: string; value: string; highlighted?: string }> = []

      // Calculate relevance score
      searchFields.forEach(field => {
        const fieldValue = (student as any)[field]?.toString().toLowerCase() || ''
        const queryLower = validatedSearch.query.toLowerCase()
        
        if (fieldValue.includes(queryLower)) {
          // Exact phrase match gets highest score
          if (fieldValue === queryLower) {
            score += 100
          } else if (fieldValue.startsWith(queryLower)) {
            score += 80
          } else {
            score += 50
          }

          // Add to matches
          const match: any = {
            field,
            value: (student as any)[field]?.toString() || ''
          }

          // Add highlighting if requested
          if (validatedSearch.options?.highlightMatches) {
            const regex = new RegExp(`(${validatedSearch.query})`, 'gi')
            match.highlighted = match.value.replace(regex, '<mark>$1</mark>')
          }

          matches.push(match)
        }

        // Individual term matching
        searchTerms.forEach(term => {
          if (term.length >= 2 && fieldValue.includes(term.toLowerCase())) {
            score += 20
          }
        })
      })

      // Boost score for active students
      if (student.status === 'ACTIVE') {
        score += 10
      }

      return {
        student: legacyStudent,
        score,
        matches: matches.length > 0 ? matches : undefined
      }
    })

    // Sort by relevance score
    results.sort((a, b) => b.score - a.score)

    const searchTime = Date.now() - startTime

    // Generate search suggestions (simple implementation)
    const suggestions: string[] = []
    if (results.length === 0) {
      // Suggest similar terms or common searches
      const commonSuggestions = [
        'Try searching by student ID',
        'Try searching by first or last name',
        'Check if the student is in a specific grade level',
        'Try a shorter search term'
      ]
      suggestions.push(...commonSuggestions.slice(0, 3))
    }

    return NextResponse.json({
      success: true,
      results,
      totalResults: results.length,
      searchTime,
      suggestions: suggestions.length > 0 ? suggestions : undefined,
      meta: {
        query: validatedSearch.query,
        filters: validatedSearch.filters,
        searchFields,
        fuzzyEnabled: validatedSearch.options?.fuzzy || false
      }
    } as SearchResponse, { status: 200 })

  } catch (error) {
    console.error('Student search error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}

// POST /api/students/search - Advanced search with complex criteria
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as ApiResponse, { status: 401 })
    }

    // Check permissions
    if (!hasPermission(session.user.role, session.user.permissions, 'students.read')) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      } as ApiResponse, { status: 403 })
    }

    const body = await request.json()
    
    // Validate search data
    const validation = searchSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid search data',
        details: validation.error.errors
      } as ApiResponse, { status: 400 })
    }

    // Use the same logic as GET but with POST body data
    // This allows for more complex search criteria that might not fit in URL params
    const validatedSearch = validation.data

    // Implementation would be similar to GET method above
    // but with the ability to handle more complex search criteria from the request body

    return NextResponse.json({
      success: true,
      message: 'Advanced search endpoint - implementation similar to GET method'
    } as ApiResponse, { status: 200 })

  } catch (error) {
    console.error('Advanced student search error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as ApiResponse, { status: 500 })
  }
}
