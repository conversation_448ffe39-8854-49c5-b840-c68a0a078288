# Student Management API Test Suite

Comprehensive test suite for the QRSAMS (QR Code Student Attendance Management System) Student Management API endpoints.

## Overview

This test suite provides comprehensive coverage for all student management API endpoints, including:

- **Unit Tests**: Individual endpoint testing with mocked dependencies
- **Integration Tests**: End-to-end testing with real database interactions
- **Authentication & Authorization**: Permission-based access control testing
- **Validation Testing**: Input validation and error handling
- **Performance Testing**: Load testing and response time validation
- **Edge Case Testing**: Boundary conditions and error scenarios

## Test Structure

```
__tests__/
├── api/
│   ├── students.test.ts              # Unit tests for all endpoints
│   └── students-integration.test.ts  # Integration tests
├── setup/
│   ├── global-setup.js              # Test environment setup
│   └── global-teardown.js           # Test cleanup
├── utils/
│   └── test-helpers.ts              # Test utilities and helpers
├── fixtures/                       # Test data files
├── temp/                           # Temporary test files
└── README.md                       # This file
```

## Test Coverage

### Core CRUD Operations
- ✅ `GET /api/students` - List students with pagination, filtering, search
- ✅ `POST /api/students` - Create new student
- ✅ `GET /api/students/[id]` - Get specific student
- ✅ `PUT /api/students/[id]` - Update student
- ✅ `DELETE /api/students/[id]` - Delete student (soft/hard)

### Advanced Query Endpoints
- ✅ `GET /api/students/grade/[level]` - Filter by grade level
- ✅ `GET /api/students/section/[section]` - Filter by section
- ✅ `GET /api/students/search` - Advanced search with fuzzy matching

### Bulk Operations
- ✅ `POST /api/students/bulk-import` - Bulk import students
- ✅ `POST /api/students/bulk-update` - Bulk update students
- ✅ `DELETE /api/students/bulk-delete` - Bulk delete students
- ✅ `GET /api/students/export` - Export student data

### QR Code Management
- ✅ `POST /api/students/[id]/qr-generate` - Generate QR code for student
- ✅ `POST /api/students/qr-bulk-generate` - Bulk QR code generation
- ✅ `POST /api/students/qr-validate` - Validate QR code

## Running Tests

### Prerequisites

1. Install dependencies:
```bash
npm install
```

2. Set up test environment:
```bash
npm run db:generate
```

### Test Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run all API tests
npm run test:api

# Run tests with verbose output
npm run test:verbose

# Debug tests
npm run test:debug

# Clear Jest cache
npm run test:clear

# Update snapshots
npm run test:update

# CI/CD pipeline tests
npm run test:ci
```

## Test Categories

### 1. Authentication & Authorization Tests
- Valid authentication with proper permissions
- Invalid authentication (no session)
- Insufficient permissions
- Role-based access control
- Permission inheritance

### 2. Validation Tests
- **DepEd ID Validation**: 12-digit format, uniqueness
- **Philippine Phone Numbers**: Mobile and landline formats
- **Email Validation**: Student and guardian emails
- **Grade Level Validation**: GRADE_7 through GRADE_12
- **Required Fields**: All mandatory fields present
- **Data Types**: Correct data type validation
- **Field Lengths**: Min/max length constraints

### 3. Business Logic Tests
- **Student Status Management**: ACTIVE, INACTIVE, TRANSFERRED, GRADUATED, DROPPED
- **Soft Delete Logic**: Status change vs hard deletion
- **Duplicate Prevention**: Student ID and email uniqueness
- **Relationship Validation**: Guardian relationships
- **Academic Year Logic**: Grade progression rules

### 4. Performance Tests
- **Response Time**: All endpoints under 2 seconds
- **Pagination Performance**: Large datasets (1000+ records)
- **Bulk Operations**: Import/export of 1000+ students
- **Concurrent Requests**: Multiple simultaneous operations
- **Memory Usage**: No memory leaks during bulk operations

### 5. Error Handling Tests
- **Database Errors**: Connection failures, constraint violations
- **Network Errors**: Timeout, connection refused
- **Validation Errors**: Detailed error messages
- **Rate Limiting**: Request throttling
- **Malformed Requests**: Invalid JSON, missing headers

### 6. Edge Cases
- **Boundary Values**: Min/max limits for all fields
- **Special Characters**: Unicode, emojis in names
- **Large Payloads**: Maximum request size handling
- **Empty Responses**: No data scenarios
- **Concurrent Updates**: Race condition handling

## Test Data

### Mock Data Generation
The test suite includes utilities for generating realistic test data:

```typescript
// Generate single student
const student = generateMockStudentData({
  gradeLevel: 'GRADE_11',
  section: 'STEM-A'
})

// Generate multiple students
const students = generateMockStudentList(50, {
  gradeLevel: 'GRADE_12'
})

// Generate grade distribution
const distribution = generateGradeDistribution()
```

### Test Fixtures
Pre-defined test data files:
- `valid-students.json` - Valid student records
- `invalid-students.json` - Invalid data for error testing
- `bulk-import-sample.csv` - CSV import sample
- `test-qr-data.json` - QR code test data

## Database Testing

### Test Database Setup
- SQLite in-memory database for fast testing
- Automatic schema migration
- Clean state between tests
- Transaction rollback for failed operations

### Data Cleanup
- Automatic cleanup after each test
- Removal of test data
- Database reset between test suites
- Temporary file cleanup

## Coverage Requirements

### Minimum Coverage Thresholds
- **Lines**: 80%
- **Functions**: 80%
- **Branches**: 80%
- **Statements**: 80%

### Critical Path Coverage
- **API Endpoints**: 85% minimum
- **Validation Logic**: 90% minimum
- **Database Operations**: 85% minimum
- **Authentication**: 90% minimum

## Continuous Integration

### GitHub Actions Integration
```yaml
- name: Run Tests
  run: npm run test:ci

- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Test Reports
- **JUnit XML**: For CI/CD integration
- **HTML Report**: Human-readable test results
- **Coverage Report**: Detailed coverage analysis
- **Performance Metrics**: Response time tracking

## Debugging Tests

### Common Issues
1. **Database Connection**: Ensure test database is properly initialized
2. **Authentication Mocks**: Verify auth mocks are properly configured
3. **Async Operations**: Use proper async/await patterns
4. **Memory Leaks**: Check for unclosed database connections

### Debug Commands
```bash
# Run specific test file
npm test -- students.test.ts

# Run specific test case
npm test -- --testNamePattern="should create student"

# Debug with Node inspector
node --inspect-brk node_modules/.bin/jest --runInBand

# Verbose logging
DEBUG=* npm test
```

## Best Practices

### Test Writing Guidelines
1. **Descriptive Names**: Clear test descriptions
2. **Arrange-Act-Assert**: Structured test organization
3. **Independent Tests**: No test dependencies
4. **Cleanup**: Proper resource cleanup
5. **Mocking**: Mock external dependencies
6. **Error Testing**: Test both success and failure paths

### Performance Considerations
1. **Parallel Execution**: Tests run in parallel by default
2. **Database Transactions**: Use transactions for data isolation
3. **Mock Heavy Operations**: Mock file I/O and external APIs
4. **Resource Limits**: Set appropriate timeouts

## Contributing

### Adding New Tests
1. Follow existing test structure
2. Use test helpers for common operations
3. Add appropriate assertions
4. Update coverage requirements if needed
5. Document any new test utilities

### Test Maintenance
1. Keep tests up-to-date with API changes
2. Refactor tests when business logic changes
3. Monitor test performance
4. Update mock data as needed

## Troubleshooting

### Common Test Failures
- **Database Lock**: Multiple tests accessing database simultaneously
- **Port Conflicts**: Test server port already in use
- **Memory Issues**: Large datasets causing OOM
- **Timeout Errors**: Slow operations exceeding limits

### Solutions
- Use test database isolation
- Configure unique test ports
- Implement data pagination in tests
- Increase timeout for integration tests

## Metrics and Monitoring

### Test Performance Tracking
- Average test execution time
- Slow test identification (>5 seconds)
- Memory usage monitoring
- Coverage trend analysis

### Quality Gates
- All tests must pass
- Coverage thresholds must be met
- No slow tests in critical path
- No memory leaks detected

---

For more information about the QRSAMS project, see the main [README.md](../README.md) file.
